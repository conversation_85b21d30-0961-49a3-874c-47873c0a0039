FROM openjdk:17

ENV LANG C.UTF-8
# 避免在构建过程中出现交互式提示
ARG DEBIAN_FRONTEND=noninteractive

VOLUME ["/home"]
VOLUME ["/logs"]

COPY aip-cmtt-1.0.0-SNAPSHOT.jar /app/aip-cmtt-1.0.0-SNAPSHOT.jar
COPY config /app/config

ENTRYPOINT ["java", "-Dfile.encoding=UTF-8", "-cp", "/home/<USER>/*:/app/aip-cmtt-1.0.0-SNAPSHOT.jar", "com.icarus.cmtt.AipCmttApplication", "--spring.profiles.active=prod", "--spring.config.additional-location=/app/config/application.yml"]