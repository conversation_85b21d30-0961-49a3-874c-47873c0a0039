<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.metateam.icarus</groupId>
        <artifactId>icarus-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.metateam.biggroup</groupId>
    <artifactId>aip-cmtt</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jackson.version>2.17.3</jackson.version>
    </properties>

    <dependencies>
        <!-- icarus 系列依赖继承父级版本 -->
        <dependency>
            <groupId>com.metateam.icarus</groupId>
            <artifactId>icarus-starter-minio</artifactId>
        </dependency>

        <dependency>
            <groupId>com.metateam.icarus</groupId>
            <artifactId>icarus-springboot-web</artifactId>
        </dependency>

        <!-- Jackson 继承父级版本 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jsonSchema</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <!-- 校验依赖继承父级版本 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Hutool 继承父级版本 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- PostgreSQL 驱动继承父级版本 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- MyBatis-Plus 依赖（使用父级管理的BOM版本） -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
        </dependency>

        <!-- SpringDoc (兼容 Spring Boot 3.x) -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.5.0</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
            <scope>compile</scope>
        </dependency>

        <!-- 测试相关依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>yss_releases</id>
            <name>YSS Releases Repository</name>
            <url>http://**********:7080/repository/yss_releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>yss_snapshots</id>
            <name>YSS Snapshots Repository</name>
            <url>http://**********:7080/repository/yss_snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>yss_releases</id>
            <name>YSS Releases Repository</name>
            <url>http://**********:7080/repository/yss_releases/</url>
        </repository>

        <snapshotRepository>
            <id>yss_snapshots</id>
            <name>YSS Snapshots Repository</name>
            <url>http://**********:7080/repository/yss_snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <!--
        依赖分离启动命令（Windows）：
        java -cp "lib/*;aip-cmtt-1.0.0-SNAPSHOT.jar" com.icarus.cmtt.AipCmttApplication
        依赖分离启动命令（Linux）：
        java -cp "lib/*:aip-cmtt-1.0.0-SNAPSHOT.jar" com.icarus.cmtt.AipCmttApplication
        -->
        <plugins>
            <!-- spring-boot-maven-plugin 插件已彻底移除，只保留 maven-jar-plugin 和 maven-dependency-plugin -->
            <!-- 复制指定环境配置文件到指定目录 -->
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources/</directory>
                                    <includes>
                                        <include>*.xml</include>
                                        <include>*.yml</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}/config</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- jar包内剔除所有配置文件 -->
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <!--不打入jar包的文件类型或者路径-->
                    <excludes>
                        <exclude>*.yml</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 打包发布时，跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <!-- 依赖分离插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <!--拷贝打完的jar包给docker工程-->
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <copy todir="docker/config/"> <!-- 目标目录 -->
                                    <fileset dir="${project.build.directory}/config/"> <!-- 源文件目录 -->
                                        <include name="**/*"/> <!-- 包含所有文件 -->
                                    </fileset>
                                </copy>
                                <echo message="生成的jar包复制给docker工程"/>
                                <copy file="${project.build.directory}/${project.artifactId}-${project.version}.jar"
                                      tofile="docker/${project.artifactId}-${project.version}.jar"
                                      overwrite="true"/>
                                <!-- 新增：复制依赖库 -->
                                <echo message="复制依赖库"/>
                                <copy todir="docker/lib">
                                    <fileset dir="${project.build.directory}/lib"/>
                                </copy>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>