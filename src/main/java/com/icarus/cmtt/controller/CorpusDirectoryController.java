package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.CorpusDirectoryDTO;
import com.icarus.cmtt.service.biz.CorpusDirectoryBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "语料库目录接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus/directory")
public class CorpusDirectoryController {
    @Resource
    private CorpusDirectoryBizService corpusDirectoryBizService;

    @Operation(summary = "语料库目录操作")
    @PostMapping("/operate")
    public Object operate(@RequestBody CorpusDirectoryDTO properties) {
        log.info("语料库目录操作: {}", properties);
        Object result;
        switch (properties.getOperation()) {
            case "query":
                result = corpusDirectoryBizService.queryCorpusDir();
                break;
            case "insert":
                result = corpusDirectoryBizService.insertCorpusDir(properties);
                break;
            case "update":
                result = corpusDirectoryBizService.updateCorpusDir(properties);
                break;
            case "delete":
                result = corpusDirectoryBizService.deleteCorpusDir(properties);
                break;
            default:
                throw new RuntimeException("无效的操作类型: " + properties.getOperation());
        }
        log.info("语料库目录操作结果: {}", new JSONObject(result));
        return result;
    }
}
