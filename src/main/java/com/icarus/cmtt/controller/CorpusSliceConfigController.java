package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.corpus.sliceconfig.ConfigCreateDTO;
import com.icarus.cmtt.dto.corpus.sliceconfig.ConfigQueryDTO;
import com.icarus.cmtt.dto.corpus.sliceconfig.ConfigUpdateDTO;
import com.icarus.cmtt.service.biz.CorpusSliceConfigBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Tag(name = "语料切片配置管理接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus/slice_config")
public class CorpusSliceConfigController {

    @Resource
    private CorpusSliceConfigBizService corpusSliceConfigBizService;

    @Operation(summary = "查询语料切片配置")
    @PostMapping("/query")
    public Object query(@RequestBody ConfigQueryDTO request) {
        log.info("语料切片配置查询: {}", request);
        Object result = corpusSliceConfigBizService.querySliceConfig(request);
        log.info("语料切片配置查询结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "更新语料切片配置")
    @PostMapping("/update")
    public Object update(@RequestBody ConfigUpdateDTO request) {
        log.info("语料切片配置更新: {}", request);
        Object result = corpusSliceConfigBizService.updateSliceConfig(request);
        log.info("语料切片配置更新结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "创建语料切片配置")
    @PostMapping("/create")
    public Object create(@RequestBody ConfigCreateDTO request) {
        log.info("语料切片配置创建: {}", request);
        Object result = corpusSliceConfigBizService.createSliceConfig(request);
        log.info("语料切片配置创建结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "删除语料切片配置")
    @DeleteMapping("/delete")
    public Object delete(@RequestParam String id) {
        log.info("语料切片配置删除: {}", id);
        Object result = corpusSliceConfigBizService.deleteSliceConfig(id);
        log.info("语料切片配置删除结果: {}", new JSONObject(result));
        return result;
    }
}
