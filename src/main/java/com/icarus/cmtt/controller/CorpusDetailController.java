package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.CorpusDetailDTO;
import com.icarus.cmtt.service.biz.CorpusDetailBizService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "语料库详情接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus/detail")
public class CorpusDetailController {
    @Resource
    private CorpusDetailBizService corpusDetailBizService;

    @PostMapping("/operate")
    public Object operate(@RequestBody CorpusDetailDTO properties) {
        log.info("语料库详情操作: {}", properties);
        Object result;
        switch (properties.getOperation()) {
            case "query":
                result = corpusDetailBizService.queryCorpusDetail(properties);
                break;
            case "create":
                result = corpusDetailBizService.insertCorpusDetail(properties);
                break;
            case "update":
                result = corpusDetailBizService.updateCorpusDetail(properties);
                break;
            case "delete":
                result = corpusDetailBizService.deleteCorpusDetail(properties);
                break;
            default:
                throw new RuntimeException("无效的操作类型: " + properties.getOperation());
        }
        log.info("语料库详情操作结果: {}", new JSONObject(result));
        return result;
    }
}
