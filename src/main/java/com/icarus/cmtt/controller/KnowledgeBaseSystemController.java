package com.icarus.cmtt.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.DifyDocumentDTO;
import com.icarus.cmtt.dto.DifyFilesDTO;
import com.icarus.cmtt.dto.KnowledgeBaseSystemDTO;
import com.icarus.cmtt.dto.KnowledgebaseDTO;
import com.icarus.cmtt.entity.CorpusScopeRule;
import com.icarus.cmtt.service.IKnowledgeBaseSystemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@RestController
@RequestMapping("/api/knowledgeBaseSystem")
public class KnowledgeBaseSystemController {

    @Autowired
    private IKnowledgeBaseSystemService knowledgeBaseSystemService;

    @PostMapping("/create")
    public Integer createKnowledgeBaseSystem(@RequestBody KnowledgebaseDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getDto())){
            return 0;
        }
        if (StringUtils.isNotBlank(dto.getId())){
            dto.getDto().setId(dto.getId());
        }

        return knowledgeBaseSystemService.addKnowledgeBaseSystem(dto.getDto());
    }

    @PostMapping("/update")
    public Integer updateKnowledgeBaseSystem(@RequestBody KnowledgebaseDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getDto())){
            return 0;
        }
        return knowledgeBaseSystemService.updateKnowledgeBaseSystem(dto);
    }

    @PostMapping("/delete")
    public Integer deleteKnowledgeBaseSystem(@RequestBody String id) {
        if (StringUtils.isBlank(id)){
            return 0;
        }
        return knowledgeBaseSystemService.deleteKnowledgeBaseSystem(id);
    }

    @GetMapping("/detail")
    public Object getKnowledgeBaseSystem(String id) {
        return knowledgeBaseSystemService.getKnowledgeBaseSystem(id);
    }

    @GetMapping("/tree")
    public String getKnowledgeBaseSystemTree() {
        return knowledgeBaseSystemService.getKnowledgeBaseSystemTree();
    }

    @PostMapping("/rename")
    public Integer renameKnowledgeBaseSystem(@RequestBody KnowledgeBaseSystemDTO dto){
        //只有id,name,property,updateTime,ModifiedBy
        return knowledgeBaseSystemService.renameKnowledgeBaseSystem(dto);
    }
    @PostMapping("/getCorpusCountByTags")
    public Long getCorpusCountByTags(@RequestBody List<CorpusScopeRule> condition){
        return knowledgeBaseSystemService.getCorpusCountByTags(condition);
    }

    @PostMapping("/saveFiles")
    public Object saveFiles(@RequestBody DifyFilesDTO difyFilesDTO){
        if (Objects.isNull(difyFilesDTO)){
            return "文档参数文档参数difyFilesDTO为null";
        }
        String difyId = difyFilesDTO.getId();
        if (StringUtils.isBlank(difyId)){
            return "文档参数difyFilesDTO中的id为空";
        }
        List<DifyDocumentDTO> difyDocumentDTOS = difyFilesDTO.getDifyDocumentDTOS();
        if (CollectionUtil.isEmpty(difyDocumentDTOS)){
            return "文档参数difyFilesDTO中的difyDocumentDTOS为空";
        }

        return knowledgeBaseSystemService.saveFiles(difyFilesDTO);
    }

}
