package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.service.ICorpusFileService;
import com.icarus.cmtt.service.biz.CorpusFileBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: TODO
 */
@RestController
@Slf4j
@Tag(name = "语料收集器接口")
@RequestMapping("/corpusFile")
public class CorpusFileController {

    @Resource
    private CorpusFileBizService corpusFileService;


    @PostMapping("/data")
    @Operation(summary = "增删改查接口")
    public Object data(@RequestBody JSONObject jsonObject){
        return corpusFileService.handle(jsonObject);
    }


}