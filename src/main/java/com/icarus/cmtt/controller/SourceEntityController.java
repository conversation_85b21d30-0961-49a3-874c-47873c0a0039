package com.icarus.cmtt.controller;


import cn.hutool.json.JSONArray;
import com.icarus.cmtt.dto.SourceEntityDTO;
import com.icarus.cmtt.service.biz.SourceEntityBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Tag(name = "来源机构接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus/source_entity")
public class SourceEntityController {

    @Resource
    private SourceEntityBizService sourceEntityBizService;

    @Operation(summary = "查询来源机构")
    @PostMapping("/operate")
    public Object getInstitution(@RequestBody SourceEntityDTO request) {
        log.info("查询来源机构");
        Object result;
        switch (request.getOperation()) {
            case "query":
                result = sourceEntityBizService.getEntity();
                break;
            case "create":
                result = sourceEntityBizService.insertEntity(request);
                break;
            case "update":
                result = sourceEntityBizService.updateEntity(request);
                break;
            case "delete":
                result = sourceEntityBizService.deleteEntity(request);
                break;
            default:
                throw new RuntimeException("无效的操作类型: " + request.getOperation());
        }
        log.info("查询来源机构结果:{}", new JSONArray(result));
        return result;
    }
}
