package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.service.biz.KnowledgeBaseBizService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @description: 知识库管理
 */
@RestController
@Slf4j
@Tag(name = "知识库管理接口")
@RequestMapping("/knowledgeManage")
public class KnowledgeBaseManageController {

    @Resource
    KnowledgeBaseBizService knowledgeBaseBizService;

    @PostMapping("/data")
    public Object data(@RequestBody JSONObject jsonObject) {
        return knowledgeBaseBizService.handle(jsonObject);
    }



}