package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.CorpusSearchDTO;
import com.icarus.cmtt.service.biz.CorpusSearchBizService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "语料搜索接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus")
public class CorpusSearchController {
    @Resource
    private CorpusSearchBizService corpusSearchBizService;

    @PostMapping("/search")
    public Object search(@RequestBody CorpusSearchDTO properties) {
        log.info("语料搜索: {}", properties);
        Object result;
        result = corpusSearchBizService.searchCorpus(properties);
        log.info("语料搜索结果：{}", new JSONObject(result));
        return result;
    }
}
