package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.corpus.slice.*;
import com.icarus.cmtt.service.biz.CorpusSliceTaskBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Tag(name = "语料切片管理接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus/slice")
public class CorpusSliceController {
    @Resource
    private CorpusSliceTaskBizService corpusSliceTaskBizService;

    @Operation(summary = "查询语料切片任务")
    @PostMapping("/query")
    public Object query(@RequestBody SliceQueryDTO request) {
        log.info("语料切片任务查询: {}", request);
        Object result = corpusSliceTaskBizService.query(request);
        log.info("语料切片任务查询结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "查询语料切片文档")
    @PostMapping("/query_documents")
    public Object queryDocuments(@RequestBody SliceQueryDocumentsDTO request) {
        log.info("语料切片文档查询: {}", request);
        Object result = corpusSliceTaskBizService.queryDocuments(request);
        log.info("语料切片文档查询结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "查询语料切片详情")
    @PostMapping("/query_segments")
    public Object querySegments(@RequestBody SliceQuerySegmentsDTO request) {
        log.info("语料切片详情查询: {}", request);
        Object result = corpusSliceTaskBizService.querySegments(request);
        log.info("语料切片详情查询结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "创建语料切片任务")
    @PostMapping("/create")
    public Object create(@RequestBody SliceCreateDTO request) {
        log.info("语料切片任务创建: {}", request);
        Object result = corpusSliceTaskBizService.create(request);
        log.info("语料切片任务创建结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "启动语料切片任务")
    @PostMapping("/start")
    public Object start(@RequestBody SliceStartDTO request) {
        log.info("语料切片任务开始: {}", request);
        Object result = corpusSliceTaskBizService.start(request);
        log.info("语料切片任务开始结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "导入语料切片文档")
    @PostMapping("/import_documents")
    public Object importDocuments(@RequestBody SliceImportDocumentsDTO request) {
        log.info("语料切片文档导入: {}", request);
        Object result = corpusSliceTaskBizService.importDocuments(request);
        log.info("语料切片文档导入结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "删除语料切片任务")
    @DeleteMapping("/delete")
    public Object delete(@RequestParam Long id) {
        log.info("语料切片任务删除: {}", id);
        Object result = corpusSliceTaskBizService.delete(id);
        log.info("语料切片任务删除结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "删除语料切片文档")
    @DeleteMapping("/delete_document")
    public Object deleteDocument(@RequestParam String id) {
        log.info("语料切片文档删除: {}", id);
        Object result = corpusSliceTaskBizService.deleteDocument(id);
        log.info("语料切片文档删除结果: {}", new JSONObject(result));
        return result;
    }
}
