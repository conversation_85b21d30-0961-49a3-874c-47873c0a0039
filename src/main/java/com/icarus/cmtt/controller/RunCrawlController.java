package com.icarus.cmtt.controller;

import com.icarus.cmtt.service.ICrawlCorpusCollectionScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 *  前端控制器 - Test
 *
 *  包含示例及规范说明
 *
 * </p>
 * @link <a href="https://cloud.tencent.com/developer/article/1897852">参考腾讯Swagger规范页面</a>
 * <AUTHOR>
 * @since 2025-06-30
 */
/*
规范：
    必备下面4个@注解
 */
@RestController
@Slf4j
@Tag(name = "调用爬虫接口") // 替换 @Api(tags = "测试接口")
@RequestMapping("/api")
public class RunCrawlController {
    /*
    规范：
        BestCase:
            1. 推荐用@Resource注解， 且jakarta包
            2. 务必在com/icarus/cmtt/service/biz目录下创建一层xxBizService层，可有效避免循环依赖
            3. 其中注入多个xxIService，不可直接注入xxMapper

     */
    @Resource
    private ICrawlCorpusCollectionScheduleService crawlCorpusCollectionScheduleService;



    @Operation(summary = "运行爬虫") // 替换 @ApiOperation
    @GetMapping("/run/crawl")
    public void runCrawl() {
        crawlCorpusCollectionScheduleService.runCrawl();
    }

}
