package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.dto.corpus.sliceconfigdir.DirectoryCreateDTO;
import com.icarus.cmtt.dto.corpus.sliceconfigdir.DirectoryQueryDTO;
import com.icarus.cmtt.dto.corpus.sliceconfigdir.DirectoryUpdateDTO;
import com.icarus.cmtt.service.biz.CorpusSliceConfigDirectoryBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Tag(name = "语料切片配置目录管理接口")
@RestController
@Slf4j
@RequestMapping("/aip/api/cmtt/corpus/slice_config_directory")
public class CorpusSliceConfigDirectoryController {

    @Resource
    private CorpusSliceConfigDirectoryBizService corpusSliceConfigDirectoryBizService;

    @Operation(summary = "查询语料切片配置目录")
    @PostMapping("/query")
    public Object query(@RequestBody DirectoryQueryDTO request) {
        log.info("语料切片配置查询: {}", request);
        Object result = corpusSliceConfigDirectoryBizService.querySliceConfigDirectory(request);
        log.info("语料切片配置查询结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "更新语料切片配置目录")
    @PostMapping("/update")
    public Object update(@RequestBody DirectoryUpdateDTO request) {
        log.info("语料切片配置更新: {}", request);
        Object result = corpusSliceConfigDirectoryBizService.updateSliceConfigDirectory(request);
        log.info("语料切片配置更新结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "创建语料切片配置目录")
    @PostMapping("/create")
    public Object create(@RequestBody DirectoryCreateDTO request) {
        log.info("语料切片配置创建: {}", request);
        Object result = corpusSliceConfigDirectoryBizService.createSliceConfigDirectory(request);
        log.info("语料切片配置创建结果: {}", new JSONObject(result));
        return result;
    }

    @Operation(summary = "删除语料切片配置目录")
    @DeleteMapping("/delete")
    public Object delete(@RequestParam String id) {
        log.info("语料切片配置删除: {}", id);
        Object result = corpusSliceConfigDirectoryBizService.deleteSliceConfigDirectory(id);
        log.info("语料切片配置删除结果: {}", new JSONObject(result));
        return result;
    }
}
