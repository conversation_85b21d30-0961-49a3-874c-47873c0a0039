package com.icarus.cmtt.controller;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.entity.PushTask;
import com.icarus.cmtt.service.biz.VectorBizService;
import com.icarus.cmtt.service.biz.PushTaskBizService;
import com.icarus.common.minio.MinioUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 推送服务
 */
@RestController
@Slf4j
@Tag(name = "推送服务")
@RequestMapping("/pushService")
public class PushServiceController {

    @Resource
    private PushTaskBizService pushTaskBizService;

    @Resource
    MinioUtil minioUtil;


    @RequestMapping("/data")
    public Object data(@RequestBody JSONObject jsonObject){
        log.info("处理推送服务请求: {}参数", jsonObject);
        Object handle = pushTaskBizService.handle(jsonObject);
        log.info("返回推送服务区结果: {}", handle);
        return handle;
    }

    @GetMapping("/push")
    public void push() throws Exception {
        log.info("开始推送");
        pushTaskBizService.push();
    }

}