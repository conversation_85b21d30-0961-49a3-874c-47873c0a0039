package com.icarus.cmtt.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icarus.cmtt.common.rest.BaseResponse;
import com.icarus.cmtt.dto.KnowledgebaseMappingDTO;
import com.icarus.cmtt.service.IKnowledgebaseMappingService;
import com.icarus.cmtt.vo.KnowledgebaseTagsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@Slf4j
@Tag(name = "语料库标签接口")
@RequestMapping("/api/corpus/knowledgebasemapping")
public class KnowledgebaseMappingController {


    @Autowired
    private IKnowledgebaseMappingService knowledgebaseMappingService;

    @Operation(summary = "创建知识库映射")
    @PostMapping("/create")
    public BaseResponse<Integer> createKnowledgebaseMapping(@RequestBody KnowledgebaseMappingDTO dto) {
        Integer result = knowledgebaseMappingService.addMappingTags(dto);
        return BaseResponse.ok( result);
    }

    @Operation(summary = "修改知识库映射")
    @PostMapping("/update")
    public BaseResponse<Integer> updateKnowledgebaseMapping(@RequestBody KnowledgebaseMappingDTO dtoUpdate) {
        Integer result = knowledgebaseMappingService.updateMappingTags(dtoUpdate);
        return BaseResponse.ok( result);
    }

    @Operation(summary = "删除知识库映射")
    @DeleteMapping("/delete")
    public BaseResponse<Integer> deleteKnowledgebaseMapping(Integer id) {
        Integer result = knowledgebaseMappingService.deleteKnowledgebaseMapping( id);
        return BaseResponse.ok( result);
    }

    @Operation(summary = "知识库映射详情")
    @GetMapping("/detail")
    public BaseResponse<KnowledgebaseTagsVo> getKnowledgebaseMapping(Integer id) {
        KnowledgebaseTagsVo result = knowledgebaseMappingService.findByBaseId(id);
        return BaseResponse.ok( result);
    }

    @Operation(summary = "知识库映射列表")
    @PostMapping("/querylist")
    public BaseResponse<IPage<KnowledgebaseTagsVo>> querylist(@RequestBody KnowledgebaseMappingDTO queryDTO) {
        IPage<KnowledgebaseTagsVo> knowledgebaseTagsVoIPage = knowledgebaseMappingService.queryKnowledgebaseMappings(queryDTO);
        return BaseResponse.ok( knowledgebaseTagsVoIPage);
    }
}
