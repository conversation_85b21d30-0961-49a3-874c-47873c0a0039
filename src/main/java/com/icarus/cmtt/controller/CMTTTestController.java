package com.icarus.cmtt.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.icarus.cmtt.enums.AipApiEnum;
import com.icarus.cmtt.service.ICorpusCollectionConfigService;
import com.icarus.cmtt.service.biz.CMTTTestBizService;
import com.icarus.cmtt.service.biz.VectorBizService;
import com.icarus.cmtt.util.AipApiClientUtil;
import com.icarus.common.minio.MinioUtil;
import io.minio.messages.Bucket;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器 - Test
 *
 *  包含示例及规范说明
 *
 * </p>
 * @link <a href="https://cloud.tencent.com/developer/article/1897852">参考腾讯Swagger规范页面</a>
 * <AUTHOR>
 * @since 2025-06-30
 */
/*
规范：
    必备下面4个@注解
 */
@RestController
@Slf4j
@Tag(name = "测试接口") // 替换 @Api(tags = "测试接口")
@RequestMapping("/test")
public class CMTTTestController {


    /*
    规范：
        BestCase:
            1. 推荐用@Resource注解， 且jakarta包
            2. 务必在com/icarus/cmtt/service/biz目录下创建一层xxBizService层，可有效避免循环依赖
            3. 其中注入多个xxIService，不可直接注入xxMapper

     */
    @Resource
    private CMTTTestBizService testBizService;

    @Resource
    private MinioUtil minioUtil;


    @Resource
    private VectorBizService vectorBizService;

    @GetMapping("/123")
    public void test()
    {
        vectorBizService.combine("1949791918816276482","corpusfile");
    }

    /*
        BadCase:
            1. 禁止使用@Autowired注解，且spring包
            2. 未在com/icarus/cmtt/service/biz目录下创建xxBizService层，直接引用IServiece或Mapper
     */
    @Autowired
    private ICorpusCollectionConfigService corpusCollectionConfigService;

    /*
        规范：
            1. 要添加@ApiOperation注解，描述接口功能
            2. path路径推荐
     */
    @Operation(summary = "测试数据库链接cmtt") // 替换 @ApiOperation
    @GetMapping("/test-db-cmtt")
    public String testCmttDataSource() {
        testBizService.testCmttDataSource();
        return "测试CMTT数据源成功";
    }

    @Operation(summary = "测试数据库链接cmtt") // 替换 @ApiOperation
    @GetMapping("/testCorpusCollectionConfig")
    public String testCorpusCollectionConfig() {
        corpusCollectionConfigService.selectIdsByFrequency(null, null, null);
        return "测试CMTT数据源成功: ";
    }

    @Operation(summary = "测试minio") // 替换 @ApiOperation
    @GetMapping("/testMinIO")
    public String testMinIO() {
        List<Bucket> allBuckets = minioUtil.getAllBuckets();
        return "测试CMTT数据源成功: " + allBuckets.stream().map(Bucket::name).collect(Collectors.joining(","));
    }

    @Operation(summary = "测试aipApi") // 替换 @ApiOperation
    @GetMapping("/testAipApi")
    public String testAipApi() {
        Map<String, String> queryParam = new HashMap<>();
//        queryParam.put("stream", "true");
        String res = AipApiClientUtil.callApi(AipApiEnum.aiSearch.getPath(), queryParam, new DemoReq().setQuestion("保险机构人才管理办法"));
        return "测试CMTT数据源成功: " + res;
    }

    @Operation(summary = "测试testAipGetCall") // 替换 @ApiOperation
    @PostMapping("/testAipGetCall")
    public String testAipGetCall(@RequestParam String path) {
        String res = AipApiClientUtil.callApi(path, null, new DemoReq().setQuestion("保险机构人才管理办法"));
        return "测试testAipGetCall: " + res;
    }

    @Operation(summary = "测试testAipPostCall") // 替换 @ApiOperation
    @PostMapping("/testAipPostCall")
    public String testAipPostCall(@RequestBody DemoReq req) {
        String res = AipApiClientUtil.callApi(AipApiEnum.aiSearch.getPath(), null, req);
        return "测试testAipPostCall: " + res;
    }

    @Operation(summary = "测试testAipCall") // 替换 @ApiOperation
    @GetMapping("/testAipCall")
    public String testAipCall() {
//    public String testAipCall(@RequestParam String path, @RequestParam String stream, @RequestBody DemoReq req) {
        JSONArray businessTag = new JSONArray();
        businessTag.put("北京");
        businessTag.put("交易所");
        businessTag.put("通知公告");
        String jsonStr = JSONUtil.toJsonStr(businessTag) + "||" + JSONUtil.parseArray(businessTag).toString();
//
//        Map<String, String> queryParam = new HashMap<>();
//        queryParam.put("stream", stream);
//        String res = AipApiClientUtil.callApi(path, queryParam, req);
        return "测试测试testAipCall: " + jsonStr;
    }

    @Accessors(chain = true)
    @Data
    public static class DemoReq {
        private String question;
    }
}
