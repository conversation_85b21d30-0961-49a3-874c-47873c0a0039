package com.icarus.cmtt.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.icarus.cmtt.common.rest.BaseResponse;
import com.icarus.cmtt.dto.CorpusTagsDTO;
import com.icarus.cmtt.entity.CorpusTags;
import com.icarus.cmtt.service.ICorpusTagsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@Slf4j
@Tag(name = "语料库标签接口")
@RequestMapping("/api/corpus/tag")
public class CorpusTagsController {


    @Autowired
    private ICorpusTagsService corpusTagsService;

    @Operation(summary = "创建标签")
    @PostMapping("/create")
    public BaseResponse<Integer> createTag(@RequestBody CorpusTagsDTO tagsDTO) {
        CorpusTags tag = tagsDTO.getTag();
        List<Double> embedding = corpusTagsService.convert(tagsDTO.getEmbedding());
        if (Objects.nonNull( tag)){
            tag.setEmbedding( embedding);
            return BaseResponse.ok(corpusTagsService.addTag(tag));
        }
        return BaseResponse.badRequest("tag参数错误");
    }

    @Operation(summary = "修改标签")
    @PostMapping("/update")
    public BaseResponse<Integer> updateTag(@RequestBody CorpusTagsDTO tagsDTO) {
        CorpusTags tag = tagsDTO.getTag();
        List<Double> embedding = corpusTagsService.convert(tagsDTO.getEmbedding());
        if (Objects.nonNull( tag)){
            tag.setEmbedding( embedding);
            return BaseResponse.ok(corpusTagsService.updateTag(tag));
        }
        return BaseResponse.badRequest("tag参数错误");
    }

    @Operation(summary = "删除标签")
    @DeleteMapping("/delete")
    public BaseResponse<Integer> deleteTag(Integer id) {
        Integer result = corpusTagsService.deleteTagById(id);
        return BaseResponse.ok(result);
    }

    @Operation(summary = "标签详情")
    @GetMapping("/detail")
    public BaseResponse<CorpusTags> getTag(Integer id) {
        CorpusTags tag = corpusTagsService.findTag(id);
        return BaseResponse.ok(tag);
    }

    @Operation(summary = "查询所有标签")
    @GetMapping("/list")
    public BaseResponse<List<CorpusTags>> getTagList() {
        return BaseResponse.ok(corpusTagsService.findTagList());
    }
}
