package com.icarus.cmtt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.entity.CorpusSliceTask;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CorpusSliceTaskVO {
    private Long id;
    private Integer status;
    private String name;
    private String databasePath;
    private String corpusName;
    private String configInfo;
    private String pushTaskId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    public CorpusSliceTaskVO(CorpusSliceTask task) {
        this.id = task.getId();
        this.status = task.getStatus();
        this.name = task.getName();
        this.createTime = task.getCreateTime();
        this.updateTime = task.getUpdateTime();
        this.startTime = task.getStartTime();
        this.endTime = task.getEndTime();
        this.pushTaskId = task.getPushTaskId();
    }
}
