package com.icarus.cmtt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 知识库管理分页列表
 */
@Data
public class KnowledgeBaseManageListVO {
    // 主键
    private String id;
    //名称
    private String name;
    private String description;
    private List<String> tags;

    private String fileDimension;
    //文件数量
    private  int fileCount;
    //字符数
    private String charCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}