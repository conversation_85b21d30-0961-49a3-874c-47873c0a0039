package com.icarus.cmtt.vo;

import com.icarus.cmtt.entity.CorpusDetail;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
@NoArgsConstructor
public class CorpusDetailVO extends CorpusDetail{
    private String sourceEntity;
    private String sourceInstitutionUrl;
    private String collectionPath;
    private String collectionPathUrl;
    private String collectionDetailPath;
    private String sourceInstitution;
    private String subSourceInstitution;

    public CorpusDetailVO(CorpusDetail corpusDetail) {
        BeanUtils.copyProperties(corpusDetail, this);
    }
}
