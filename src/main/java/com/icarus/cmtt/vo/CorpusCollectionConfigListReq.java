package com.icarus.cmtt.vo;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CorpusCollectionConfigListReq {

    private String id;

    /**
     * 来源机构大类
     */
    private String sourceInstitution;

    /**
     * 来源机构子类
     */
    private String subSourceInstitution;

    /**
     * 来源机构链接
     */
    private String sourceInstitutionUrl;

    /**
     * 语料收集路径(来源路径)
     */
    private String collectionPath;

    /**
     * 收集路径链接
     */
    private String collectionPathUrl;

    /**
     * 来源明细路径
     */
    private String collectionDetailPath;

    /**
     * 收集类型
     */
    private String collectionType;

    /**
     * 收集频率
     */
    private List<JSONObject> frequency;


    /**
     * 语料库路径
     */
    private String corpusDatabasePath;

    /**
     * 创建人
     */
    private String owner;

    /**
     * 更新时间
     */
    private Date updateTime;

    /*
    *  权限 public 和 private
    * */
    private String permission;

    /*
    * 路径属性
    * */
    private String pathAttribute;


    @ApiModelProperty("收集渠道")
    private String collectionChannel;

    @ApiModelProperty("处理内容")
    private String executeContent;

    @ApiModelProperty("处理方式")
    private String executeWay;

    @ApiModelProperty("处理详情")
    private String executeDetail;

    @Data
    public static class CollectionFrequency{

        /*
        *   '每个自然日', value: 'NATURAL_DAY'
            '每个工作日', value: 'WORK_DAY'
        * */
        String type;

        String time;
    }
}
