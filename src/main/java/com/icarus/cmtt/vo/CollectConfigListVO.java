package com.icarus.cmtt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 分页展示对象
 */
@Data
public class CollectConfigListVO {
    private String id;
    private Integer status;
    private String collectionChannel;
    private String collectionType;
    //来源主体
    private String sourceEntity;
    private String collectionPathUrl;
    private String corpusDatabasePath;
    private List<Object> frequency;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executeTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String owner;
}