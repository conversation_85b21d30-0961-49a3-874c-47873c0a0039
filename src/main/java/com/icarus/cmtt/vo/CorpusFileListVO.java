package com.icarus.cmtt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 语料文件
 */
@Data
public class CorpusFileListVO {
    private String id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String corpusId;

    private String name;

    private String property;

    private String type;

    private String fileSize;

    private String minioFileKey;

}