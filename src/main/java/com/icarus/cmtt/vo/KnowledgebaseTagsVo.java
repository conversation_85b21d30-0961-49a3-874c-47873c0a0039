package com.icarus.cmtt.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.entity.CorpusTags;
import com.icarus.cmtt.entity.KnowledgebaseMapping;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Data
public class KnowledgebaseTagsVo {
    // 主键
    private Integer id;

    // 知识库映射名称
    private String name;

    // 标签
    private String tags;

    // 关联标签
    private List<Tag> relatedTags;

    // 创建人
    private String createdBy;

    // 修改人
    private String modifiedBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;

    // 修改时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;

    // 关联语料数量
    private Integer corpusCount;

    @Data
    @Accessors(chain = true)
    public static class Tag{
        private Integer id;
        private String name;
    }



    public static KnowledgebaseTagsVo buildKnowledgebaseMappingVo(KnowledgebaseMapping mapping, List<KnowledgebaseMappingTags> mappingTagsList, List<CorpusTags> tagsList){
        if (mapping == null) {
            return null;
        }

        KnowledgebaseTagsVo vo = new KnowledgebaseTagsVo();
        vo.setId(mapping.getId());
        vo.setName(mapping.getName());
        vo.setCreatedBy(mapping.getCreatedBy());
        vo.setModifiedBy(mapping.getModifiedBy());
        vo.setCreateTime(mapping.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        vo.setUpdateTime(mapping.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 处理关联标签
        ArrayList<Tag> tagArrayList = new ArrayList<Tag>();
        //关联表不为空的时候设置标签id
        if (!CollectionUtils.isEmpty(mappingTagsList)) {
            for(int i = 0; i < mappingTagsList.size(); i++) {
                Integer tagId = mappingTagsList.get(i).getTagId();
                Tag tag = new Tag().setId(tagId);
                //语料表不为空的时，设置标签名称
                if (!CollectionUtils.isEmpty(tagsList)){
                    CorpusTags corpusTags = tagsList.stream()
                            .filter(item -> item.getId().equals(Integer.valueOf(tagId)))
                            .findFirst().orElse(null);
                    if ( Objects.nonNull(corpusTags)){
                        tag.setName(corpusTags.getName());
                    }
                }
                tagArrayList.add(tag);
            }
        }
        vo.setRelatedTags(tagArrayList);
        return vo;
    }

    /**
     * 给前端返回tag的id和name
     * @param vo
     * @param tagsList
     */
    public static void setTagName(KnowledgebaseTagsVo vo, List<CorpusTags> tagsList){
        if (Objects.isNull(vo) || StrUtil.isBlank(vo.getTags())){
            return;
        }
        String[] tagIds = vo.getTags().split(",");
        ArrayList<Tag> tagArrayList = new ArrayList<Tag>();
        for (String tagId : tagIds) {
            CorpusTags corpusTags = tagsList.stream()
                    .filter(tag -> tag.getId().equals(Integer.valueOf(tagId)))
                    .findFirst().orElse(null);

            if ( Objects.nonNull(corpusTags)){
                Tag tag = new Tag().setId(Integer.valueOf(tagId)).setName(corpusTags.getName());
                tagArrayList.add(tag);
            }
        }
        vo.setRelatedTags(tagArrayList);
    }


}
