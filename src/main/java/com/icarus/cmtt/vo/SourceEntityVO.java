package com.icarus.cmtt.vo;

import com.icarus.cmtt.entity.CorpusDictionary;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SourceEntityVO {
    private String id;
    private String parentId;
    private String dictValue;
    private String dictType;
    private String sourceInstitutionUrl;
    private String description;

    public SourceEntityVO(CorpusDictionary corpusDictionary) {
        BeanUtils.copyProperties(corpusDictionary, this);
    }
}
