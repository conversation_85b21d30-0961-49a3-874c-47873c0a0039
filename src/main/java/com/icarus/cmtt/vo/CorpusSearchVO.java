package com.icarus.cmtt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.entity.CorpusDetail;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CorpusSearchVO {
    private String id;
    private String name;
    private String description;
    private String sourceEntity;
    private String[] tags;
    private String officialNumber;
    private String collectionPath;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime publishDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    private String owner;

    public CorpusSearchVO(CorpusDetail detail){
        this.id = detail.getId();
        this.name = detail.getName();
        this.description = detail.getDescription();
        this.officialNumber = detail.getOfficialNumber();
        this.publishDate = detail.getPublishDate();
        this.updateTime = detail.getUpdateTime();
        this.owner = detail.getOwner();
    }
}
