package com.icarus.cmtt.typeHandler;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedJdbcTypes(JdbcType.OTHER)
public class PgVectorListTypeHandler extends BaseTypeHandler<List<Double>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Double> parameter, JdbcType jdbcType) throws SQLException {
        String vectorString = parameter.toString().replace(" ", "");
        ps.setObject(i, vectorString, Types.OTHER);
    }

    @Override
    public List<Double> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseVector(value);
    }

    @Override
    public List<Double> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseVector(value);
    }

    @Override
    public List<Double> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseVector(value);
    }

    private List<Double> parseVector(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        value = value.replaceAll("^\\[|\\]$", "");
        if (value.isEmpty()) {
            return new ArrayList<>();
        }
        String[] parts = value.split(",");
        List<Double> result = new ArrayList<>();
        for (String part : parts) {
            result.add(Double.parseDouble(part));
        }
        return result;
    }
}