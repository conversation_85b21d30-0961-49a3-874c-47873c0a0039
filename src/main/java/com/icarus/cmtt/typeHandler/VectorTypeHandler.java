package com.icarus.cmtt.typeHandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostgreSQL向量类型处理器
 * <p>
 * 用于处理Java的String与PostgreSQL的vector类型之间的转换
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@MappedTypes(String.class)
public class VectorTypeHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        // 创建PostgreSQL对象
        if (parameter != null && parameter.startsWith("[") && parameter.length() > 1 && parameter.charAt(1) != '"' && parameter.endsWith("]")) {
            // 只有符合向量格式的字符串才使用vector类型
            // 确保是以[开头，但第二个字符不是"（排除JSON数组），并且以]结尾
            PGobject pgObject = new PGobject();
            pgObject.setType("vector");
            pgObject.setValue(parameter);
            ps.setObject(i, pgObject);
        } else {
            // 其他字符串使用普通字符串类型
            ps.setString(i, parameter);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        return obj == null ? null : obj.toString();
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        return obj == null ? null : obj.toString();
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        return obj == null ? null : obj.toString();
    }
} 