package com.icarus.cmtt.enums;

import lombok.Getter;

/**
 * @description: 语料收集器枚举
 */
@Getter
public enum OperationType {
    /** 创建记录 */
    CREATE,
    /** 删除记录 */
    DELETE,
    /** 更新记录 */
    UPDATE,
    /** 获取单条记录 */
    GET,
    /** 分页查询记录 */
    QUERY,
    /** 枚举 */
    ENUM,
    /** 上传文件 */
    UPLOAD,
    /** 获取树形结构*/
    TREE,
    /** 列表 */
    LIST,
    /**
     * 子查询
     */
    QUERY_CHILD,
    /**
     * 关联
     */
    COMBINE

    ;
}