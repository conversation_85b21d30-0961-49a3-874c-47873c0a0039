package com.icarus.cmtt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum AipApiEnum {

    aiSearch("/ai/search/aiSearch", "智能问答粗糙版本"),
    vector("/corpus/pushService/vector", "向量库操作节点"),
    fileSlice("/corpus/slice/file", "文件切片");

    private String path;

    private String description;

}
