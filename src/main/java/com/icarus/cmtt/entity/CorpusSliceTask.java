package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 语料切片任务实体类
 * 对应数据库表：corpus_slice_task
 */
@Data
@TableName("corpus_slice_task")
public class CorpusSliceTask implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("corpus_database_id")
    private String corpusDatabaseId;

    @TableField("corpus_id")
    private String corpusId;

    @TableField("config_id")
    private String configId;

    @TableField("mode")
    private Integer mode;

    @TableField("name")
    private String name;

    @TableField("status")
    private Integer status;

    @TableField("push_task_id")
    private String pushTaskId;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    public CorpusSliceTask() {
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateTime(LocalDateTime.now());
        this.setStatus(0);
    }
}
