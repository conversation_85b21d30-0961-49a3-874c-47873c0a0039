package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("knowledge_base_embedding")
@ApiModel(value = "KnowledgeBaseEmbedding对象", description = "")
public class KnowledgeBaseEmbedding implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("embedding_id")
    private Object embeddingId;

    private String articleSummary;

    private Object metadata;

    private Object embedding;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String minioFileKey;

    private String title;

    private Object summaryEmbedding;

    private String corpusId;

    private String knowledgeBaseItemSegmentId;
}
