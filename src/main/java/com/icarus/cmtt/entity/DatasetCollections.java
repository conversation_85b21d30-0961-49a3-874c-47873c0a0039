package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("dataset_collections")
@ApiModel(value = "DatasetCollections对象", description = "")
public class DatasetCollections implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String datasetId;

    private String fileId;

    private String name;

    private String parentId;

    private String teamId;

    private String tmbId;

    private String trainingType;

    private String type;

    private Object tag;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;

    private String intro;

    private String permission;

    private String dialogueId;
}
