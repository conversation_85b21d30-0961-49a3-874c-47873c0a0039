package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("dataset_data")
@ApiModel(value = "DatasetData对象", description = "")
public class DatasetData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String a;

    private String q;

    private Integer version;

    private String chunkIndex;

    private String collectionId;

    private String datasetId;

    private String fullTextToken;

    private String indexes;

    private String teamId;

    private String tmbId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
