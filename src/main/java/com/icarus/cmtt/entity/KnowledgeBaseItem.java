package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("knowledge_base_item")
@ApiModel(value = "KnowledgeBaseItem对象", description = "")
public class KnowledgeBaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String corpusId;

    private String sourceFileId;

    private String title;

    private String remark;

    private String brief;

    private Boolean isEmbedded;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Boolean isParse;

    private String profession;

    private Boolean isSaveEs;

    private String articleSummary;

    private Integer crawlContentId;

    @ApiModelProperty("0 等待处理， 1 处理中，2处理完成 ，3 处理失败")
    private String knowledgeStatus;

    @ApiModelProperty("后续应该没用了")
    private String exceptionDetails;

    private String knowledgeTemplate;

    private Boolean isTag;

    @ApiModelProperty("知识化项目id")
    private Integer knowledgeProjectId;
}
