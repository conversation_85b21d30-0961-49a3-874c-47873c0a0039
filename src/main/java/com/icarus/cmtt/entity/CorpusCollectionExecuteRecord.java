package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_collection_execute_record")
@ApiModel(value = "CorpusCollectionExecuteRecord对象", description = "")
public class CorpusCollectionExecuteRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String corpusCollectionConfigId;

    private String batchId;

    private String executeDay;

    private String executeTime;

    private Boolean isExecuteSuccess;

    private Boolean isSave;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @ApiModelProperty("0 正在收集 1 收集完成 2需要进行语料收集 3语料处理完成")
    private Integer executeStatus;

    private Integer crawlSettingId;
}
