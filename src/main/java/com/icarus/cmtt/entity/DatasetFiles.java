package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("dataset_files")
@ApiModel(value = "DatasetFiles对象", description = "")
public class DatasetFiles implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private Integer chunkSize;

    private String fileName;

    private LocalDateTime uploadDate;

    private String datasetId;

    private Short isNew;

    private String esFileKey;

    private String minioFileKey;
}
