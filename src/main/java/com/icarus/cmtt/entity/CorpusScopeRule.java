package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Getter
@Setter
@TableName("corpus_scope_rule")
@ApiModel(value = "CorpusScopeRule对象", description = "")
public class CorpusScopeRule implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @TableField("system_id")
    private String systemId;

    private String operator;

    private String value;

    @TableField("logic_with_prev")
    private String logicWithPrev;

    @TableField(value = "\"order\"")
    private String order;

    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    @TableField(value = "group_id")
    private String groupId;

    @TableField(value = "group_order")
    private String groupOrder;

    @TableField(value = "tag_type")
    private String tagType;

    private String name;
}
