package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@TableName("crawl_setting")
@Data
@ApiModel(value = "crawlSetting对象", description = "")
public class CrawlSetting {

    private Long id;

    private String schedule;

    private Short speed;

    private Short concReq;

    private String header;

    private String cookie;

    private Date createTime;

    private String status;

    private Boolean isDelete;

    private String title;

    private String clickListPath;

    private String getTitle;

    private String getPubTime;

    private String getContent;

    private String nextPagePath;


    private String crawlUrl;

    private String iframe;

    private String crawlFunction;

    private String mainExtraInfo;

	private Integer finalState;



}

