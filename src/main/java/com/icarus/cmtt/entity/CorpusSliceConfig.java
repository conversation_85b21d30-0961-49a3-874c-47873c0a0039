package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 语料切片配置实体类
 * 对应数据库表：corpus_slice_config
 */
@Data
@TableName("corpus_slice_config")
public class CorpusSliceConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    @TableField("name")
    private String name;

    @TableField("cot_id")
    private String cotId;

    @TableField("status")
    private Integer status;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("slice_size")
    private Integer sliceSize;

    @TableField("overlap_size")
    private Integer overlapSize;

    @TableField("description")
    private String description;

    @TableField("directory_id")
    private String directoryId;
}
