package com.icarus.cmtt.entity;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.icarus.cmtt.typeHandler.FrequencyTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_collection_config")
@ApiModel(value = "CorpusCollectionConfig对象", description = "")
public class CorpusCollectionConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String sourceEntity;

    private String sourceInstitutionUrl;

    private String collectionType;

    private String collectionFrequency;

    private String collectionPath;

    private String collectionPathUrl;

    private String profession;

    private String corpusDatabasePath;

    private String owner;

    private String collectionDetailPath;

    private Integer crawlSettingId;

    @ApiModelProperty("所属主题")
    private String subject;

    private String pathAttribute;

    private String permission;

    @ApiModelProperty("频率 新")
    @TableField(typeHandler = FrequencyTypeHandler.class)
    private String frequency;

    //新增字段
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("收集渠道")
    private String collectionChannel;

    private String executeContent;

    private String executeWay;

    private String executeDetail;

    private String executeDetailId;

    private LocalDateTime executeTime;

}
