package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@TableName("crawl_content")
@ApiModel(value = "crawlContent", description = "")
public class CrawlContent {

    private Long id;

    private String crawlTitle;

	private String crawlContentTitle;

    private Date crawlPubTime;

    private String crawlContent;

    private String crawlFile;

    private Integer crawlSettingId;

    private String crawlUrl;

    private Long crawlTime;

    private String mainExtraInfo;


	private Boolean corpusStatus;

    private String crawlBatchId;

}

