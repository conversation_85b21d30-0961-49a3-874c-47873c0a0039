package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("push_task")
@ApiModel(value = "pushTask对象", description = "推送任务表")
public class PushTask {

    private String id;
    @ApiModelProperty("推送任务状态 0  待启动 1 进行中 2 已完成 3 有故障")
    private Integer status;
    private String corpusDictionary;
    private String corpusName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    private String corpusId;


}