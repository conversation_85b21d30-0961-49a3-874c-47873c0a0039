package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("knowledge_project")
@ApiModel(value = "KnowledgeProject对象", description = "")
public class KnowledgeProject implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty("项目名称")
    private String name;

    private String corpusId;

    private String corpusName;

    @ApiModelProperty("知识化状态 0 等待处理， 1 处理中，2处理完成 ，3 处理失败")
    private Integer knowledgeStatus;

    private Short isNew;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String description;

    @ApiModelProperty("知识化方式  1 向量")
    private Integer knowledgeType;
}
