package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 历史问题表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("question_history")
@ApiModel(value = "QuestionHistory对象", description = "历史问题表")
public class QuestionHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("原始问题")
    private String questionContext;

    @ApiModelProperty("拆分出的问题关键字")
    private String keywords;

    @ApiModelProperty("评分星级  1-5对应相应星级 默认0")
    private String ratingStar;

    @ApiModelProperty("知识库分类 0通用 1新闻 ")
    private String knowledgeSort;

    @ApiModelProperty("问题向量")
    private Object questionEmbedding;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("回答")
    private String answer;

    @ApiModelProperty("相关机构")
    private Object relatedInstitutions;

    @ApiModelProperty("相关业务")
    private Object relatedBusiness;

    @ApiModelProperty("相关搜索")
    private Object recommendSearch;

    @ApiModelProperty("相关语料片段id")
    private String knowledgeFileIds;

    @ApiModelProperty("意图来源")
    private String intentionSource;

    @ApiModelProperty("是否删除 1：是 0：否")
    private Integer isDel;

    @ApiModelProperty("状态 0成功 1失败 2 限流")
    private String status;

    @ApiModelProperty("失败原因")
    private String failReason;

    @ApiModelProperty("会话id")
    private String dialogueid;

    @ApiModelProperty("收藏0未收藏1已收藏")
    private String collectStatus;

    @ApiModelProperty("搜索入口 1主聊天框2追问3拓展搜索4优化搜索")
    private String searchPortal;

    @ApiModelProperty("ai评分星级  1-5对应相应星级 默认0")
    private String aiRatingStar;

    @ApiModelProperty("语料内容片段数据")
    private String knowledge;

    @ApiModelProperty("业务标签")
    private String businessTags;

    @ApiModelProperty("评分状态 0未评分 1已评分")
    private String ratingStatus;

    @ApiModelProperty("回答结束时间")
    private LocalDateTime endTime;

    private String ip;

    @ApiModelProperty("1. 深度思考 2. 文档问答 3. 知识库问答")
    private Integer questionType;

    @ApiModelProperty("对话id，对话指1问1答")
    private String conversationId;

    @ApiModelProperty("浏览器密匙")
    private String browserKey;
}
