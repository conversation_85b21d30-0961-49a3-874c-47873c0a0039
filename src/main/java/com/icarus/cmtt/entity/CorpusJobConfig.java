package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_job_config")
@ApiModel(value = "CorpusJobConfig对象", description = "")
public class CorpusJobConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String jobId;

    private Integer jobStatus;

    private String robotUuid;

    private String accountName;

    private Integer robotType;

    private String expandColumn1;

    private String expandColumn2;
}
