package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_dictionary")
@ApiModel(value = "CorpusDictionary对象", description = "")
public class CorpusDictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String dictType;

    private String dictValue;

    private String description;

    private String sortOrder;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String parentId;

    @ApiModelProperty("收集路径链接")
    private String collectionPathUrl;

    @ApiModelProperty("路径属性")
    private String pathAttr;

    @ApiModelProperty("来源机构链接")
    private String sourceInstitutionUrl;
}
