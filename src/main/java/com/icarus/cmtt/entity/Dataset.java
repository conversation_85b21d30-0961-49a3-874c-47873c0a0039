package com.icarus.cmtt.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@ApiModel(value = "Dataset对象", description = "")
public class Dataset implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String name;

    private String intro;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String parentId;

    private String permission;

    private String type;

    private Boolean canWrite;

    private Boolean isOwner;

    private Object tag;

    private String classes;

    private String attribute;
}
