package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 语料切片段落实体类
 * 对应数据库表：corpus_slice_segment
 */
@Data
@TableName("corpus_slice_segment")
public class CorpusSliceSegment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 文档ID
     */
    @TableField("document_id")
    private String documentId;

    /**
     * 语料切片任务ID
     */
    @TableField("slice_task_id")
    private Long sliceTaskId;

    /**
     * 索引编号
     */
    @TableField("index")
    private Integer index;

    /**
     * 内容文本
     */
    @TableField("content")
    private String content;

    /**
     * 文件ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 切片长度
     */
    @TableField("length")
    private Integer length;
}