package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 样本库管理表，用于存储样本库和版本数据，采用自引用结构实现层级关系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("sample_library")
@ApiModel(value = "SampleLibrary对象", description = "样本库管理表，用于存储样本库和版本数据，采用自引用结构实现层级关系")
public class SampleLibrary implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID，自增")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("名称（样本库名称或版本名称）")
    private String name;

    @ApiModelProperty("描述（样本库或版本的详细说明）")
    private String description;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty("是否启用（true表示启用，false表示已禁用）")
    private Boolean isActive;

    @ApiModelProperty("上级ID，NULL表示根级样本库，非NULL表示版本")
    private Integer parentId;

    @ApiModelProperty("层级：1-样本库 2-版本")
    private Integer level;

    @ApiModelProperty("版本号(level=2时使用，如\"v1.0.0\"或日期型版本等)")
    private String version;

    @ApiModelProperty("文件路径(level=2时使用，存储样本文件的实际路径)")
    private String filePath;

    @ApiModelProperty("文件类型(level=2时使用，如\"csv\"、\"json\"等)")
    private String fileType;

    @ApiModelProperty("文件名称")
    private String fileName;
}
