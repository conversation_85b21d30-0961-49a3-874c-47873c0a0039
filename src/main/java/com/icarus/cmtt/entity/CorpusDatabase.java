package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_database")
@ApiModel(value = "CorpusDatabase对象", description = "")
public class CorpusDatabase implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String parentId;

    private String name;

    private String intro;

    private String permission;

    private String type;

    private String canWrite;

    private String isOwner;

    private String tag;

    private String classes;

    private String attribute;

    @ApiModelProperty("主题")
    private String subject;
}
