package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Getter
@Setter
@TableName("knowledge_base_system")
@ApiModel(value = "KnowledgeBaseSystem对象", description = "")
public class KnowledgeBaseSystem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String name;

    private String property;

    @TableField(value = "parent_id")
    private String parentId;

    private String description;

    @TableField(value = "associated_system")
    private String associatedSystem;

    @TableField(value = "connect_mode")
    private String connectMode;

    private String ip;

    private String username;

    private String password;

    private String api;

    private String apiKey;

    @TableField(value = "slice_type")
    private String sliceType;

    @TableField(value = "slice_count")
    private Integer sliceCount;

    @TableField(value = "\"order\"")
    private String order;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "modified_by")
    private String modifiedBy;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "dify_id")
    private String difyId;

    @TableField(value = "file_dimension")
    private String fileDimension;
}
