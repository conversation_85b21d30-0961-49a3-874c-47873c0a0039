package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_session_record")
@ApiModel(value = "CorpusSessionRecord对象", description = "")
public class CorpusSessionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String wechatGroupId;

    private String wechatGroupName;

    private String wechatUserId;

    private String wechatUserName;

    private String wechatSessionInfo;

    private Boolean isReply;

    private Integer wechatSessionLabel;

    private Integer wechatSessionOthers;

    private String seatUserId;

    private String userEmotion;

    private String replyTo;

    private String sendStatus;
}
