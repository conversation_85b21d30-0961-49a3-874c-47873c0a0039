package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 语料切片配置目录实体类
 * 对应数据库表：corpus_slice_config_directory
 */
@Data
@TableName("corpus_slice_config_directory")
public class CorpusSliceConfigDirectory implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    @TableField("name")
    private String name;

    @TableField("parent_id")
    private String parentId;
}