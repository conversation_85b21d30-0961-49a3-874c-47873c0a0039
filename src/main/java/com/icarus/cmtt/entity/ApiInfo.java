package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("api_info")
@ApiModel(value = "ApiInfo对象", description = "")
public class ApiInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.NONE)
    private Long id;

    @ApiModelProperty("功能名字")
    private String name;

    @ApiModelProperty("功能描述与接口文档")
    private String description;

    @ApiModelProperty("负责人")
    private String responsiblePerson;

    @ApiModelProperty("调用地址")
    private String url;

    @ApiModelProperty("请求方式")
    private String requestType;

    @ApiModelProperty("系统名")
    private String systemName;
}
