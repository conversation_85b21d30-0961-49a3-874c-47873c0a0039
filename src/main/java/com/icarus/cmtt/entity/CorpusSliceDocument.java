package com.icarus.cmtt.entity;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.typeHandler.ArrayTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 语料切片文档实体类
 * 对应数据库表：corpus_slice_document
 */
@Data
@TableName("corpus_slice_document")
public class CorpusSliceDocument implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 文件ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 切片任务ID
     */
    @TableField("slice_task_id")
    private Long sliceTaskId;

    /**
     * 文档标题
     */
    @TableField("title")
    private String title;

    /**
     * 切片数量
     */
    @TableField("number")
    private Integer number;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("status")
    private Integer status;

    @TableField("import_method")
    private String importMethod;

    @TableField(value = "tags", typeHandler = ArrayTypeHandler.class)
    private String[] tags;

    @TableField("file_path")
    private String filePath;

    @TableField(exist = false)
    private List<String> tagIds;

    public CorpusSliceDocument() {
        this.setId(IdUtil.objectId());
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateTime(LocalDateTime.now());
        this.setTags(new String[]{});
        this.setStatus(0);
    }
}