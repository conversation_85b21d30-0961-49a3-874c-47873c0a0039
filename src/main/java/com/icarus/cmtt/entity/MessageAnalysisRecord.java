package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("message_analysis_record")
@ApiModel(value = "MessageAnalysisRecord对象", description = "")
public class MessageAnalysisRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private Integer userMessageId;

    private String intentionRecognition;

    private String queryKb;

    private String reasoningAnalysis;

    private String recommendScript;

    private String reRecommend;

    private String createTime;

    private String updateTime;
}
