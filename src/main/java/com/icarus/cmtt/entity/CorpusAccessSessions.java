package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_access_sessions")
@ApiModel(value = "CorpusAccessSessions对象", description = "")
public class CorpusAccessSessions implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private LocalTime createTime;

    private LocalTime updateTime;

    private Integer accessType;

    private String accessName;

    private Integer sessionsType;

    private Integer agentAssignmentStatus;

    private Integer sessionProcessingStatus;
}
