package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 搜索数据统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("search_record_statistics")
@ApiModel(value = "SearchRecordStatistics对象", description = "搜索数据统计表")
public class SearchRecordStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("搜索次数")
    private Integer searchTimes;

    @ApiModelProperty("搜索人数")
    private Integer searchVolume;

    @ApiModelProperty("追问次数")
    private Integer probeTimes;

    @ApiModelProperty("收藏次数")
    private Integer colletcTimes;

    @ApiModelProperty("评分次数")
    private Integer ratingTimes;

    @ApiModelProperty("修正次数")
    private Integer correctTimes;

    @ApiModelProperty("好评次数")
    private Integer goodRatingTimes;

    @ApiModelProperty("新增用户数")
    private Integer newUserTimes;

    @ApiModelProperty("活跃用户数")
    private Integer activeUserTimes;

    @ApiModelProperty("反馈次数")
    private Integer feedbackTimes;

    @ApiModelProperty("用户角色分布信息")
    private Object userRoleDistribution;

    @ApiModelProperty("意图分布信息")
    private Object intentionDistribution;

    @ApiModelProperty("高频语料分布信息")
    private Object corpusDistribution;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("统计数据类型，1日数据，2小时数据")
    private String statisticsType;

    @ApiModelProperty("用户id 为空的是汇总数据")
    private Long userId;

    @ApiModelProperty("搜索时长毫秒数")
    private Long searchDuration;

    @ApiModelProperty("搜索轮次")
    private Integer searchRoundTimes;
}
