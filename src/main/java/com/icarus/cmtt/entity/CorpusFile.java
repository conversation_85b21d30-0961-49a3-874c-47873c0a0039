package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_file")
@ApiModel(value = "CorpusFile对象", description = "")
public class CorpusFile implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String corpusId;

    private String name;

    private String type;

    private String esFileKey;

    private String minioFileKey;

    private Integer isNew;

    private Integer fileSize;

    private String filePath;

    private String versionNumber;

    @ApiModelProperty("提取出的语料名称")
    private String corpusName;
}
