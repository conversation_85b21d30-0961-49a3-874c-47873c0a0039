package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("ip_user_mapping")
@ApiModel(value = "IpUserMapping对象", description = "")
public class IpUserMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String ipAddress;

    private String username;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;
}
