package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 远程接口配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("remote_api_config")
@ApiModel(value = "RemoteApiConfig对象", description = "远程接口配置表")
public class RemoteApiConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("配置ID")
    private String id;

    @ApiModelProperty("接口名称")
    private String apiName;

    @ApiModelProperty("接口描述")
    private String description;

    @ApiModelProperty("基础URL")
    private String baseUrl;

    @ApiModelProperty("接口路径")
    private String path;

    @ApiModelProperty("请求方法(GET, POST等)")
    private String httpMethod;

    @ApiModelProperty("内容类型")
    private String contentType;

    @ApiModelProperty("超时时间(毫秒)")
    private Integer timeoutMs;

    @ApiModelProperty("重试次数")
    private Integer retryCount;

    @ApiModelProperty("是否需要认证")
    private Boolean needAuth;

    @ApiModelProperty("认证类型(Bearer, Basic等)")
    private String authType;

    @ApiModelProperty("认证Token/密钥")
    private String authToken;

    @ApiModelProperty("请求头(JSON格式)")
    private String headers;

    @ApiModelProperty("是否启用")
    private Boolean enabled;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("所属工程")
    private String belongProject;
}
