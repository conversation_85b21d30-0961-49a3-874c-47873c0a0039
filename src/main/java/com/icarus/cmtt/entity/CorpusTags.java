package com.icarus.cmtt.entity;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.typeHandler.PgVectorListTypeHandler;
import lombok.Getter;
import lombok.Setter;

/**
 * 语料库
 */

@Getter
@Setter
@TableName("corpus_tags")
public class CorpusTags implements Serializable {

    private static final long serialVersionUID = 1L;

	//标签id
	@TableId(value = "id")
    private Integer id;

	//分组或者标签名称
	private String name;

	//同义词
	private String synonym;

	//标签描述
	private String description;

	//标签类型1是group，2是tag
	private Integer type;

	@TableField(value = "group_id")
	private Integer groupId;

	//标签层级
	private Integer level;

	//向量
	@TableField(typeHandler = PgVectorListTypeHandler.class)
	private List<Double> embedding;

	//父级id
	@TableField("parent_id")
	private Integer parentId;

	//创建时间
	@TableField("create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	//修改时间
	@TableField("update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	//创建人
	@TableField("created_by")
	private String createdBy;

	//修改人
	@TableField("modified_by")
	private String modifiedBy;

	@TableField("feature")
	private Integer feature;
}
