package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 系统用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("sys_user")
@ApiModel(value = "SysUser对象", description = "系统用户表")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("用户类型（00系统用户）")
    private String userType;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("性别 1：男 2：女  3：未知")
    private Integer sex;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("组织id")
    private Long deptId;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("加密言")
    private String salt;

    @ApiModelProperty("状态 1：有效  0：无效")
    private String status;

    @ApiModelProperty("租户id")
    private Long tenancyId;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("修改人")
    private Long updateBy;

    @ApiModelProperty("是否删除 1：是 0：否")
    private Integer isDel;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
}
