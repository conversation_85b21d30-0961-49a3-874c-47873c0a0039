package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.typeHandler.ArrayTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("corpus_detail")
@ApiModel(value = "CorpusDetail对象", description = "")
public class CorpusDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String corpusDatabaseId;

    private String name;

    private String description;

    private String publishContent;

    private String publishContentUrl;

    private String officialNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime publishDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidDate;

    @ApiModelProperty("创建人")
    private String owner;

    @ApiModelProperty("权限")
    private String permission;

    @ApiModelProperty("0  预发布 1已发布")
    private Integer status;

    private String corpusCollectionConfigId;

    private String crawlContentId;

    private String crawlTitle;

    private String versionNumber;
    //标签
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] tags;
    //关键字
    private String keyWords;
    //概要
    private String summary;

    @ApiModelProperty("valid 生效版本， invalid 失效版本")
    private String versionIdentity;

    @ApiModelProperty("主题")
    private String subject;

    @ApiModelProperty("批次id")
    private String batchId;

    @ApiModelProperty("0 迭代 1 新增")
    private Integer isNew;

    private Integer collectionStatus;

    @ApiModelProperty("0 未知识化， 1 已知识化  ")
    private Integer knowledgeStatus;

    @ApiModelProperty("修订背景")
    private String revisionBackground;

    @ApiModelProperty("修订内容")
    private String revisionContent;

    @ApiModelProperty("影响范围")
    private String reach;

    @ApiModelProperty("收集方式 CRAWL爬虫 MANUAL手动")
    private String collectionType;

    @TableField(exist = false)
    private String collectionPath;
}
