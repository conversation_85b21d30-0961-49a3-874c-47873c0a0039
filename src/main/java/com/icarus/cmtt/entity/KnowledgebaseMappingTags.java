package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.boot.autoconfigure.domain.EntityScan;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("knowledge_base_mapping_tags")
public class KnowledgebaseMappingTags implements Serializable {

    private static final long serialVersionUID = 1L;

    // 主键
    private Integer id;

    // 标签id
    @TableField("tag_id")
    private Integer tagId;

    @TableField("knowledgebase_mapping_id")
    private Integer knowledgebaseMappingId;

    // 创建时间
    @TableField("create_time")
    private LocalDateTime createTime;

    // 修改时间
    @TableField("update_time")
    private LocalDateTime updateTime;

    

}
