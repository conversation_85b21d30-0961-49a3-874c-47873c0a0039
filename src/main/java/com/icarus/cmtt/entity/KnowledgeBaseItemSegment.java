package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("knowledge_base_item_segment")
@ApiModel(value = "KnowledgeBaseItemSegment对象", description = "")
public class KnowledgeBaseItemSegment implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String corpusId;

    private String sourceFileId;

    private String knowledgeBaseItemId;

    private String title;

    @ApiModelProperty("语料内容只在这个表里存一次")
    private String content;

    private Boolean isEmbedded;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @ApiModelProperty("目前没有参考")
    private Boolean isSaveEs;

    @ApiModelProperty("片段的概要")
    private String summary;

    private String exceptionDetails;

    private String knowledgeTemplate;

    @ApiModelProperty("分片总数")
    private Integer fragmentNumber;

    @ApiModelProperty("分片页码")
    private Integer index;
}
