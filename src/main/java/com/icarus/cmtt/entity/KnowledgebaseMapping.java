package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("knowledge_base_mapping")
@ApiModel(value = "KnowledgebaseMapping对象", description = "")
public class KnowledgebaseMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    // 主键
    private Integer id;

    // 知识库映射名称
    private String name;

    // 创建人
    @TableField("created_by")
    private String createdBy;

    // 修改人
    @TableField("modified_by")
    private String modifiedBy;

    // 创建时间
    @TableField("create_time")
    private LocalDateTime createTime;

    // 修改时间
    @TableField("update_time")
    private LocalDateTime updateTime;

    

}
