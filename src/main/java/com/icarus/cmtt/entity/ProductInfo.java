package com.icarus.cmtt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@Setter
@TableName("product_info")
@ApiModel(value = "ProductInfo对象", description = "产品信息表")
public class ProductInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.NONE)
    private Long id;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品类型")
    private String productType;

    @ApiModelProperty("产品管理人")
    private String productManager;

    @ApiModelProperty("产品托管人")
    private String productCustodian;

    @ApiModelProperty("产品成立日期")
    private String establishmentDate;

    @ApiModelProperty("认购费率")
    private String subscriptionRate;

    @ApiModelProperty("投资范围")
    private String investmentScope;

    @ApiModelProperty("业绩比较基准")
    private String performanceBenchmark;

    @ApiModelProperty("投资限制")
    private String investmentRestrictions;

    @ApiModelProperty("投资经理")
    private String investmentManager;

    @ApiModelProperty("管理费率")
    private String managementFeeRate;

    @ApiModelProperty("管理费计算公式")
    private String managementFeeFormula;

    @ApiModelProperty("管理费计提方式")
    private String managementFeeAccrual;

    @ApiModelProperty("管理费支付方式")
    private String managementFeePayment;

    @ApiModelProperty("托管费率")
    private String custodyFeeRate;

    @ApiModelProperty("托管费计算公式")
    private String custodyFeeFormula;

    @ApiModelProperty("托管费计提方式")
    private String custodyFeeAccrual;

    @ApiModelProperty("托管费支付方式")
    private String custodyFeePayment;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
