package com.icarus.cmtt.scheduleCrawl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.cmtt.entity.*;
import com.icarus.cmtt.mapper.*;
import com.icarus.cmtt.service.IDataProcessorService;
import com.icarus.common.minio.MinioUtil;
import io.minio.errors.MinioException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class DataProcessorServiceImpl implements IDataProcessorService {

    @Value("${file.crawlFilePath}")
    String crawlFilePath;

    @Resource
    CrawlContentMapper crawlContentMapper;

    @Resource
    CorpusDetailMapper corpusDetailMapper;

    @Resource
    CorpusFileMapper corpusFileMapper;

    @Resource
    CorpusDatabaseMapper corpusDatabaseMapper;

    @Resource
    CorpusCollectionExecuteRecordMapper corpusCollectionExecuteRecordMapper;

    @Resource
    MinioUtil minioUtil;

    // 将Date转换为LocalDateTime的辅助方法
    private LocalDateTime convertToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    @NotNull
    private CorpusDetail saveCorpusDetail(CrawlContent crawlContent, Date date, CorpusCollectionConfig corpusCollectionConfig, String parentId, String batchId) {
        // 日志：开始保存语料详情
        log.info("开始保存语料详情，爬虫内容ID：{}", crawlContent.getId());
        CorpusDetail byCrawlContentId = corpusDetailMapper.findByCrawlContentId(crawlContent.getId().toString());
        if (byCrawlContentId != null) {
            log.info("语料详情已存在，直接返回，ID：{}", byCrawlContentId.getId());
            return byCrawlContentId;
        }

        CorpusDetail corpusDetail = new CorpusDetail();
        //todo 远程调用预处理获取、关键字、概要，标签
        String corpusDetailId = IdUtil.objectId();
        corpusDetail.setId(corpusDetailId);
        corpusDetail.setCreateTime(convertToLocalDateTime(date));
        corpusDetail.setUpdateTime(convertToLocalDateTime(date));
        corpusDetail.setCorpusDatabaseId(parentId);
        corpusDetail.setCorpusCollectionConfigId(corpusCollectionConfig.getId());
        // 兜底取爬虫title，后处理提取
        corpusDetail.setName(crawlContent.getCrawlTitle());
        corpusDetail.setCrawlTitle(crawlContent.getCrawlTitle());
        corpusDetail.setCrawlContentId(crawlContent.getId().toString());
        if(batchId != null){
            corpusDetail.setBatchId(batchId); // 批次id
        }
        corpusDetail.setPublishContentUrl(crawlContent.getCrawlUrl());
        corpusDetail.setOwner("晓赢");
        corpusDetail.setStatus(1); // 已发布状态
        corpusDetail.setVersionIdentity("valid"); // 生效状态
        corpusDetail.setDescription(crawlContent.getCrawlContent());
        corpusDetail.setPublishDate(convertToLocalDateTime(crawlContent.getCrawlPubTime()));
        corpusDetail.setCollectionType(corpusCollectionConfig.getCollectionType());
        corpusDetail.setKnowledgeStatus(0);
        String subject = corpusCollectionConfig.getSubject();
        if (StringUtils.isBlank(subject)) {
            subject = "金融"; // 金融
        }
        corpusDetail.setSubject(subject);
        // 日志：插入语料详情
        log.info("插入语料详情，ID：{}", corpusDetailId);
        corpusDetailMapper.insert(corpusDetail);
        return corpusDetailMapper.selectById(corpusDetailId);
    }

    /**
     * 定时任务收集语料
     * @param corpusCollectionConfig 配置
     * @return 结果
     * @throws IOException IO异常
     */
    @Override
    public String insertDataByCollectionConfig(CorpusCollectionConfig corpusCollectionConfig, String batchId) throws IOException {
        // 日志：开始插入数据
        log.info("开始插入数据，配置ID：{}，批次ID：{}", corpusCollectionConfig.getId(), batchId);
        try {
            // 拿到爬虫配置表id
            Integer crawlSettingId = corpusCollectionConfig.getCrawlSettingId();
            log.info("爬虫配置表id：{}， 批次id：{}", crawlSettingId, batchId);
            // 查询当前批次当前爬虫配置下的爬虫内容
            List<CrawlContent> crawlContentList = crawlContentMapper.findByCrawlSettingIdAndCrawlBatchId(crawlSettingId.intValue(), batchId);
            log.info("当前批次当前爬虫配置下的爬回数据条数：{}", crawlContentList.size());
            for (CrawlContent crawlContent : crawlContentList) {
                try {
                    // 日志：处理单条语料
                    log.info("处理单条语料，爬虫内容ID：{}", crawlContent.getId());
                    CorpusDetail corpusDetail = handleCorpus(corpusCollectionConfig, crawlContent, batchId);
                    // 语料处理完成
                    corpusCollectionExecuteRecordMapper.updateByCorpusCollectionConfigIdAndBatchId(corpusCollectionConfig.getId(), batchId);
                    log.info("语料处理完成，语料详情ID：{}", corpusDetail.getId());
                } catch (Exception e) {
                    log.error("语料收集异常 ：  url{}   title:{}", crawlContent.getCrawlUrl(), crawlContent.getCrawlTitle(), e);
                }
            }
        } catch (Exception e) {
            log.error("爬虫数据获取异常 ：  url{}   id:{}", corpusCollectionConfig.getCollectionPathUrl(), corpusCollectionConfig.getId(), e);
        }
        return null;
    }

    /**
     * 保存语料库架构（目录）
     * @param date 时间
     * @param parentId 父级ID
     * @param s1 目录名
     * @return 目录ID
     */
    public String saveCorpusDataBase(Date date, String parentId, String s1) {
        String s = s1;
        CorpusDatabase database = corpusDatabaseMapper.findByNameAndParentId(s, parentId);//两个参数都相等才能查出来
        if (database == null) {
            CorpusDatabase corpusDatabase = new CorpusDatabase();
            String id = IdUtil.objectId();
            corpusDatabase.setId(id);
            corpusDatabase.setCreateTime(convertToLocalDateTime(date));
            corpusDatabase.setUpdateTime(convertToLocalDateTime(date));
            corpusDatabase.setName(s);
            corpusDatabase.setParentId(parentId);
            parentId = id;
            corpusDatabaseMapper.insert(corpusDatabase);
            // 日志：新建语料库架构
            log.info("新建语料库架构, corpusDatabase={}", JSONUtil.toJsonStr(corpusDatabase));
        } else {
            parentId = database.getId();
            // 日志：已存在语料库架构
            log.info("已存在语料库架构, corpusDatabaseId={}", parentId);
        }
        return parentId;
    }

    /*
     * 处理语料
     */
    public CorpusDetail handleCorpus(CorpusCollectionConfig corpusCollectionConfig, CrawlContent crawlContent, String batchId){
        Date date = new Date();
        // 日志：开始处理语料
        log.info("开始处理语料，爬虫内容ID：{}", crawlContent.getId());
        String corpusDatabasePath = corpusCollectionConfig.getCorpusDatabasePath();
        // 创建路径
        String[] split = corpusDatabasePath.split("/");
        String parentId = null;
        for (String s : split) {
            // 语料库架构
            parentId = saveCorpusDataBase(date, parentId, s);
        }
        // 构建语料明细
        CorpusDetail corpusDetail = saveCorpusDetail(crawlContent, date, corpusCollectionConfig, parentId, batchId);
        // 构建语料文件
        String crawlFile = crawlContent.getCrawlFile();
        JSONArray jsonArray = JSONUtil.parseArray(crawlFile);
        // 先把旧的语料文件删了，防止特殊情况二次收集的时候重复写入
        corpusFileMapper.deleteByCorpusId(corpusDetail.getId());
        for (int i = 0; i < jsonArray.size(); i++) {
            try {
                // 日志：保存单个语料文件
                log.info("保存单个语料文件，语料详情ID：{}，文件序号：{}", corpusDetail.getId(), i);
                saveMinioCorpusFile(date, corpusDetail, jsonArray, i);
            } catch (IOException | MinioException e) {
                log.error("保存语料文件异常，语料详情ID：{}，文件序号：{}，异常：{}", corpusDetail.getId(), i, e.getMessage());
                throw new RuntimeException(e);
            }
        }
        // 更新语料处理状态
        corpusDetailMapper.updateCollectionStatusById(corpusDetail.getId(), 1);
        log.info("语料处理状态已更新，语料详情ID：{}", corpusDetail.getId());
        return corpusDetail;
    }

    /**
     * 保存Minio语料文件
     * @param date 时间
     * @param corpusDetail 语料详情
     * @param jsonArray 文件数组
     * @param i 文件序号
     * @return CorpusFile
     * @throws IOException IO异常
     * @throws MinioException Minio异常
     */
    public CorpusFile saveMinioCorpusFile(Date date, CorpusDetail corpusDetail, JSONArray jsonArray, int i) throws IOException, MinioException {
        JSONObject jsonObject = jsonArray.getJSONObject(i);
        String fileName = jsonObject.getStr("file_name");
        String filePath = jsonObject.getStr("file_path");
        String uploadPath = filePath + "/";
        String path = crawlFilePath + filePath.split("/home/<USER>")[1] + "/" + fileName;//docker文件位置的处理
        File file = new File(path);
        uploadPath = "20250708/c676cb08ace06da81730508180623/长盛基金-训练01.pdf";
        byte[] file1 = minioUtil.getFile("长盛基金-训练01.pdf");
        String minioFileKey = minioUtil.uploadFile(file1,"cmtt-minio",uploadPath, "application/pdf");
//        String minioFileKey = minioUtil.uploadFile(path, uploadPath);
        // 保存语料文件
        CorpusFile corpusFile = new CorpusFile();
        corpusFile.setCorpusId(corpusDetail.getId());
        corpusFile.setCorpusName(corpusDetail.getName());
        corpusFile.setName(fileName);
        corpusFile.setType(Files.probeContentType(file.toPath()));
        corpusFile.setMinioFileKey(minioFileKey);
        corpusFile.setId(IdUtil.objectId());
        corpusFile.setCreateTime(convertToLocalDateTime(date));
        corpusFile.setUpdateTime(convertToLocalDateTime(date));
        corpusFile.setFileSize((int) file.length());
        corpusFile.setFilePath(filePath);
        corpusFileMapper.insert(corpusFile);
        // 日志：保存语料文件成功
        log.info("保存语料文件成功，corpusFileId={}，corpusId={}", corpusFile.getId(), corpusFile.getCorpusId());
        return corpusFile;
    }
    public static Date convertToDate(String dateString) throws ParseException {
        if(dateString == null){
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.parse(dateString);
    }


}
