package com.icarus.cmtt.scheduleCrawl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.cmtt.dto.CorpusCollectionConfigListReq;
import com.icarus.cmtt.entity.CorpusCollectionConfig;
import com.icarus.cmtt.entity.CorpusCollectionExecuteRecord;
import com.icarus.cmtt.entity.CrawlSetting;
import com.icarus.cmtt.enums.CorpusCollectionTypeEnum;
import com.icarus.cmtt.mapper.CorpusCollectionConfigMapper;
import com.icarus.cmtt.mapper.CorpusCollectionExecuteRecordMapper;
import com.icarus.cmtt.mapper.CrawlSettingMapper;
import com.icarus.cmtt.service.ICrawlCorpusCollectionScheduleService;
import com.icarus.cmtt.service.IDataProcessorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CrawlCorpusCollectionScheduleServiceImpl implements ICrawlCorpusCollectionScheduleService {

	@Value("${yss.crawl.url}")
	private String crawlUrl;

	@Resource
	private RestTemplate restTemplate;

	@Resource
	IDataProcessorService dataProcessor;

	@Resource
	CrawlSettingMapper crawlSettingMapper;

	@Resource
	CorpusCollectionConfigMapper corpusCollectionConfigMapper;

	@Resource
	CorpusCollectionExecuteRecordMapper corpusCollectionExecuteRecordMapper;

	@Async
	@Override
	public void runCrawl( ){
		// 日志：开始执行定时爬虫收集任务
		log.info("开始执行定时爬虫收集任务");
		String currentHourMinute;
		LocalTime now = LocalTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		currentHourMinute = now.format(formatter);
		// 获取当前日期
		LocalDate today = LocalDate.now();
		DayOfWeek dayOfWeek = today.getDayOfWeek();
		// 是周末
		boolean isWeekend = dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

		// 查询所有爬虫类型的语料收集配置
		List<CorpusCollectionConfig> corpusCollectionConfigList = corpusCollectionConfigMapper.findByCollectionChannel(CorpusCollectionTypeEnum.CRAWL.getCode());
		// 日志：查询到的配置数量
		log.info("查询到的爬虫类型语料收集配置数量：{}", corpusCollectionConfigList.size());
		List<CorpusCollectionConfig> list = corpusCollectionConfigList.stream().filter((config)->{
			List<CorpusCollectionConfigListReq.CollectionFrequency> collectionFrequencyList = getCollectionFrequency(config);
			for (CorpusCollectionConfigListReq.CollectionFrequency collectionFrequency : collectionFrequencyList) {
				// 如果是每个自然日，时间相等则返回
				if(collectionFrequency.getType().equals("NATURAL_DAY") && collectionFrequency.getTime().equals(currentHourMinute)){
					log.info("匹配到自然日收集配置，配置ID：{}，时间：{}", config.getId(), currentHourMinute);
					return true;
				}
				// 如果是每个工作日，当不是周末，时间相等返回
				if(collectionFrequency.getType().equals("WORK_DAY") && !isWeekend && collectionFrequency.getTime().equals(currentHourMinute)){
					log.info("匹配到工作日收集配置，配置ID：{}，时间：{}", config.getId(), currentHourMinute);
					return true;
				}
			}
			return  false;
		}).collect(Collectors.toList());
		// 日志：最终需要执行的配置数量
		log.info("最终需要执行的语料收集配置数量：{}", list.size());
		// 执行语料收集任务
		executeTaskCrawl(list, currentHourMinute);
	}

	public String getExternalContent(String url) {
		try {
			// 日志：开始请求外部爬虫服务
			log.info("请求外部爬虫服务，URL：{}", url);
			String content = restTemplate.getForObject(url, String.class);
			log.info("外部爬虫服务返回内容：{}", content);
			return content;
		} catch (Exception e) {
			log.error("请求外部爬虫服务异常，URL：{}，异常：{}", url, e.getMessage());
			e.printStackTrace();
			return null;
		}
	}

	private List<CorpusCollectionConfigListReq.CollectionFrequency> getCollectionFrequency(CorpusCollectionConfig corpusCollectionConfig) {
		List<CorpusCollectionConfigListReq.CollectionFrequency> collectionFrequencyList = new ArrayList<>();
		JSONArray jsonArray = JSONUtil.parseArray(corpusCollectionConfig.getFrequency());
		for(int i=0; i< jsonArray.size();i++){
			JSONObject jsonObject = jsonArray.getJSONObject(i);
			CorpusCollectionConfigListReq.CollectionFrequency collectionFrequency =JSONUtil.toBean(jsonObject, CorpusCollectionConfigListReq.CollectionFrequency.class);
			collectionFrequencyList.add(collectionFrequency);
		}
		return collectionFrequencyList;
	}

	public void executeTaskCrawl(List<CorpusCollectionConfig> corpusCollectionConfigList, String currentHourMinute) {

		if(corpusCollectionConfigList.isEmpty()){
			log.info("没有需要处理的语料收集配置");
			return;
		}

		// 批次id，每次收集是一个批次
//		String batchId = IdUtil.objectId();
		String batchId = "67257288e4b04f7e3c2bb216";
		log.info("本次收集批次ID：{}", batchId);

		for (CorpusCollectionConfig corpusCollectionConfig : corpusCollectionConfigList) {

			// 拼接爬虫调用URL
//			String url = crawlUrl + "/crawler/start?id=" + corpusCollectionConfig.getCrawlSettingId() + "&batch_id=" + batchId;
			// 日志：调用爬虫服务
//			log.info("调用爬虫服务，配置ID：{}，URL：{}", corpusCollectionConfig.getId(), url);
//			String externalContent = getExternalContent(url);
			// 保存语料收集记录
			saveCollectionRecord(corpusCollectionConfig.getId(), corpusCollectionConfig.getCrawlSettingId() ,batchId, currentHourMinute);
			//更改执行时间
			corpusCollectionConfig.setExecuteTime(LocalDateTime.now());
			corpusCollectionConfigMapper.updateById(corpusCollectionConfig);
			// 记录爬虫执行情况
//			log.info("爬虫执行情况: 爬虫id :{}  执行情况：{}  执行时间：{}", corpusCollectionConfig.getCrawlSettingId(), externalContent, LocalDateTime.now());
		}

		log.info("语料收集配置条数：{}", corpusCollectionConfigList.size());

		ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

		// 60秒后执行检查状态
		log.info("60秒后将检查本批次收集状态，批次ID：{}", batchId);
		scheduler.schedule(()->{
			checkExecuteStatus(batchId);
		},60,TimeUnit.SECONDS);

	}

	// 检查收集状态
	private void checkExecuteStatus(String batchId){
		// 日志：开始检查收集状态
		log.info("开始检查收集状态，批次ID：{}", batchId);
		// 每分钟检查一下是否爬完
		new Thread(() -> {
			boolean allFinished = false;
			while (!allFinished){
				log.info("检查语料收集知识化是否完成，批次ID：{}", batchId);
				allFinished = true;
				// 获取当前批次的收集记录
				List<CorpusCollectionExecuteRecord> list = corpusCollectionExecuteRecordMapper.findByBatchId(batchId);
				for (CorpusCollectionExecuteRecord corpusCollectionExecuteRecord : list) {
					// 跳过已经处理过的和已完成的
					if(corpusCollectionExecuteRecord.getExecuteStatus().equals(3) || corpusCollectionExecuteRecord.getExecuteStatus().equals(1)){
						continue;
					}
					allFinished = false;
					Integer crawlSettingId = corpusCollectionExecuteRecord.getCrawlSettingId();
					CrawlSetting crawlSetting = crawlSettingMapper.selectById(corpusCollectionExecuteRecord.getCrawlSettingId());
					if(crawlSetting == null){
						log.info("爬虫配置不存在，id为：{}", crawlSettingId);
						continue;
					}
					boolean isFinished = ObjectUtil.isNotEmpty(crawlSetting.getFinalState());
					log.info("爬虫配置id:{} 是否完成：{}", crawlSettingId, isFinished);
					if(isFinished){
						// 更新语料收集记录的状态
						corpusCollectionExecuteRecord.setUpdateTime(LocalDateTime.now());
						corpusCollectionExecuteRecord.setExecuteStatus(1);
						corpusCollectionExecuteRecordMapper.insert(corpusCollectionExecuteRecord);
						try {
							// 对收集完成的语料进行处理
							CorpusCollectionConfig corpusCollectionConfig = corpusCollectionConfigMapper.selectById(corpusCollectionExecuteRecord.getCorpusCollectionConfigId());
							log.info("开始处理语料，配置ID：{}，批次ID：{}", corpusCollectionExecuteRecord.getCorpusCollectionConfigId(), batchId);
							// 语料处理
							dataProcessor.insertDataByCollectionConfig(corpusCollectionConfig, batchId);
							log.info("语料处理完成，配置ID：{}，批次ID：{}", corpusCollectionExecuteRecord.getCorpusCollectionConfigId(), batchId);
						} catch (IOException e) {
							log.error("处理语料异常，配置ID：{}，异常：{}", corpusCollectionExecuteRecord.getCorpusCollectionConfigId(), e.getMessage());
							throw new RuntimeException(e);
						}
					}
				}
				if(allFinished){
					log.info("语料收集已完成，批次ID：{}", batchId);
				}
				try {
					Thread.sleep(60000);
				} catch (InterruptedException e) {
					log.error("检查收集状态线程被中断，异常：{}", e.getMessage());
				}
			}
		}).start();
	}

	private void saveCollectionRecord(String corpusCollectionConfigId, Integer crawlSettingId, String batchId, String collectionFrequency){
		// 日志：保存收集记录
		log.info("保存收集记录，配置ID：{}，爬虫ID：{}，批次ID：{}，执行时间：{}", corpusCollectionConfigId, crawlSettingId, batchId, collectionFrequency);
		CorpusCollectionExecuteRecord corpusCollectionExecuteRecord = new CorpusCollectionExecuteRecord();
		corpusCollectionExecuteRecord.setBatchId(batchId);
		corpusCollectionExecuteRecord.setCrawlSettingId(crawlSettingId);
		corpusCollectionExecuteRecord.setCorpusCollectionConfigId(corpusCollectionConfigId);//语料配置id
		corpusCollectionExecuteRecord.setCreateTime(LocalDateTime.now());
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		corpusCollectionExecuteRecord.setExecuteDay(sdf.format(date));
		corpusCollectionExecuteRecord.setExecuteTime(collectionFrequency);
		corpusCollectionExecuteRecord.setExecuteStatus(0); // 进行中
		corpusCollectionExecuteRecordMapper.insert(corpusCollectionExecuteRecord);
		log.info("收集记录保存成功，记录ID：{}", corpusCollectionExecuteRecord.getId());
	}


}
