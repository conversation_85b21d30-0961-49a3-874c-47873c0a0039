package com.icarus.cmtt.util;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: TODO
 */
@Slf4j
public class DifyUtil {

    public final static String preUrl = "/datasets/";

    public final static String createByText = "/document/create-by-text";

    public final static String getDocuments = "/documents";


    /**
     * 通过文本创建文档
     * @param baseUrl 基础URL
     * @param datasetId 数据集ID
     * @param apiKey API密钥
     * @param documentName 文档名称
     * @param text 文本内容
     * @return 相应结果
     */
    public static String createDocumentByText(String baseUrl,
                                              String datasetId,
                                              String apiKey,
                                              String documentName,
                                              String text) {
        String url = baseUrl+preUrl + datasetId + createByText;

        // 构建请求体
        JSONObject processRule = JSONUtil.createObj().set("mode", "automatic");
        JSONObject requestBody = JSONUtil.createObj()
                .set("name", documentName)
                .set("text", text)
                .set("indexing_technique", "high_quality")
                .set("process_rule", processRule);

        String result = null;
        // 发送请求并获取响应
        try {
            log.info("开始创建文档,参数:{}",requestBody);
            result = HttpRequest.post(url)
                    .header("Authorization", "Bearer " + apiKey)
                    .header("Content-Type", "application/json")
                    .body(requestBody.toString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();
            log.info("创建文档成功,结果:{}",result);
        } catch (HttpException e) {
            log.info("创建文档异常:{}",e.getMessage());
        }
        return result;
    }

    /**
     * 获取指定数据集的文档列表
     * @param baseUrl 基础URL
     * @param datasetId 知识库id
     * @param apiKey API密钥
     * @return 所有文档列表
     */
    public static String getDocumentsFromDatabase(String baseUrl,
                                                   String datasetId,
                                                   String apiKey){
        String url = baseUrl+preUrl + datasetId +getDocuments;

        String page = "1";
        String limit = "100";

        // 构建查询参数
        String queryParams = String.format("?page=%s&limit=%s", page, limit);
        //第一次请求
        String result = null;
        try {
            log.info("开始获取数据集文档列表, datasetId：{}", datasetId);
            result = HttpRequest.get(url + queryParams)
                    .header("Authorization", "Bearer " + apiKey)
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("获取数据集文档列表成功,结果{}", result);
        } catch (HttpException e) {
            log.error("获取数据集文档列表异常:{}", e.getMessage());
        }
        if (result != null){
            JSONObject jsonObject = JSONUtil.parseObj(result);
            if (jsonObject.getBool("has_more")){
                JSONArray data = jsonObject.getJSONArray("data");
                int total = Integer.parseInt(jsonObject.getStr("total"));
                int time = total / Integer.parseInt(limit) + 2;
                for (int i = 2; i <time; i++){
                    String queryParams2 = String.format("?page=%s&limit=%s", i, limit);
                    String response = HttpRequest.get(url + queryParams2)
                            .header("Authorization", "Bearer " + apiKey)
                            .timeout(30000) // 30秒超时
                            .execute()
                            .body();
                    if (StrUtil.isNotBlank(response)){
                        JSONObject jsonRes = JSONUtil.parseObj(response);
                        data.addAll(jsonRes.getJSONArray("data"));
                    }
                }
                jsonObject.set("data",data);
                jsonObject.set("has_more",false);
                jsonObject.set("limit", total);
                result = jsonObject.toString();
            }
        }
        return result;
    }


    /**
     * 删除指定知识库指定文档
     * @param baseUrl 基础URL
     * @param datasetId 知识库id
     * @param documentId 文档id
     * @param apiKey API密钥
     * @return 操作结果
     */
    public static String deleteDocument(String baseUrl,
                                                   String datasetId,
                                                   String documentId,
                                                   String apiKey){
        // 构建查询参数
        String url = baseUrl+preUrl + datasetId +getDocuments+"/"+documentId;
        String result = null;
        try {
            log.info("开始删除文档, datasetId：{}", datasetId);
            result = HttpRequest.delete(url)
                    .header("Authorization", "Bearer " + apiKey)
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();
            log.info("删除文档成功,结果{}", result);
        }catch (Exception e){
            log.error("删除文档失败:{}", e.getMessage());
        }
        return result;
    }

}