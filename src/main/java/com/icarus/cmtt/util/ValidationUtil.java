package com.icarus.cmtt.util;

import java.util.Collection;
import java.util.function.Supplier;

public class ValidationUtil {

    public static <T extends Collection<?>> T ensureNotEmpty(T collection, String name) {
        if (isNullOrEmpty(collection)) {
            throw new IllegalArgumentException(String.format("%s cannot be null or empty", name));
        }

        return collection;
    }

    public static boolean isNullOrEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }



    public static <T> T ensureNotNull(T object, String param) {
        if (object == null) {
            throw new IllegalArgumentException(String.format("%s cannot be null",param));
        }
        return object;
    }

    public static int ensureBetween(Integer i, int min, int max, String name) {
        if (i == null || i < min || i > max) {
            throw new IllegalArgumentException(String.format("%s must be between %s and %s, but is: %s", name, min, max, i));
        }
        return i;
    }

}
