package com.icarus.cmtt.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.cmtt.config.AppConfigs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * AIP-Platform 接口调用工具类
 *      1. 默认读app-configs下的url和basePath配置， basePath会配置对应到 “产品/项目”（/cmtt）
 *      2. 以下path参数入参都从“子系统”开始往后补全： 例如： /ai/search/aiSearch
 */
public class AipApiClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(AipApiClientUtil.class);
    public static final int TIMEOUT = 50000;

    public static String callApi(String path, Object body) {
        try {
            String url = AppConfigs.aip().getSysReqUrl() + path;
            String requestBody = JSONUtil.toJsonStr(body);
            logger.info("Calling API: {} with request body: {}", url, requestBody);

            String response = HttpUtil.post(url, requestBody, TIMEOUT);

            logger.info("API response: {}", response);
            return response;
        } catch (Exception e) {
            logger.error("Error occurred while calling API", e);
            throw e;
        }
    }

    public static String callApi(String path, Map<String, String> queryParam, Object body) {
        try {
            if (queryParam == null) {
                return callApi(path, body);
            } else {
                if (!queryParam.isEmpty()) {
                    StringBuffer queryParamStr = new StringBuffer(path + "?");
                    queryParam.forEach((k, v) -> queryParamStr.append(k).append("=").append(v));
                    path = queryParamStr.toString();
                }
            }
            
            String url = AppConfigs.aip().getSysReqUrl() + path;
            String requestBody = JSONUtil.toJsonStr(body);
            logger.info("Calling API with query parameters: {} with request body: {}", url, requestBody);
            
            String response = HttpUtil.post(url, requestBody, TIMEOUT);
            
            logger.info("API response: {}", response);
            return response;
        } catch (Exception e) {
            logger.error("Error occurred while calling API with query parameters", e);
            throw e;
        }
    }
}
