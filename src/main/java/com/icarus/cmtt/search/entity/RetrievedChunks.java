package com.icarus.cmtt.search.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("retrieved_chunks")
public class RetrievedChunks {

    private String id;

    private String subQuestionId;

    private String knowledgebaseId;

    private String fileId;

    private String fileName;

    private String chunkId;

    private LocalDateTime retrievedAt;

    private Integer rankPosition;

    private String retrievedContent;
}
