package com.icarus.cmtt.search.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Setter
@Getter
@TableName("question_history")
public class QuestionHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String id;                // 查询唯一标识

    @TableField("user_id")
    private String userId;            // 用户ID

    @TableField("session_id")
    private String sessionId;         // 会话ID

    @TableField("question_context")
    private String questionContext;   // 原始查询问题

    @TableField("create_time")
    private LocalDateTime createTime;     // 查询时间

    @TableField("answer")
    private String answer;            // 最终答案

    @TableField("end_time")
    private LocalDateTime endTime;        // 回答完成时间

    @TableField("status")
    private String status;            // 处理状态 (0:成功 1:失败 2:限流)

    @TableField("execute_plan")
    private String executePlan;       // 执行计划 (JSONB)

    @TableField("rating_star")
    private String ratingStar;        // 用户评分星级(1-5星)

    @TableField("rating_status")
    private String ratingStatus;      // 评分状态 (0:未评分 1:已评分)

    @TableField("ai_rating_star")
    private String aiRatingStar;      // AI自评星级(1-5星)

    @TableField("fail_reason")
    private String failReason;        // 失败原因

    @TableField("collect_status")
    private String collectStatus;     // 收藏状态 (0:未收藏 1:已收藏)

    @TableField("search_portal")
    private String searchPortal;      // 搜索入口类型 (1:主聊天框 2:追问 3:拓展搜索 4:优化搜索)

    @TableField("ip")
    private String ip;                // 用户IP地址

    @TableField("intention")
    private String intention;         // 问题意图

    @TableField("is_del")
    private Integer isDel;            // 删除标记 (0:未删除 1:已删除)

    @TableField("dialogue_id")
    private String dialogueId;        // 会话ID
}
