package com.icarus.cmtt.search.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 子问题实体类
 * 对应数据库表：sub_questions
 * 同时用于输出响应
 */
@Getter
@Setter
@TableName("sub_question")
public class SubQuestion {

    /**
     * 子问题唯一标识 - 主键
     */
    private String id;

    /**
     * 主查询ID - 外键，关联user_queries表
     */
    private String userQueryId;

    /**
     * 子问题文本 - 大模型分解后的具体子问题
     */
    private String questionText;

    /**
     * 子问题序号 - 在主问题中的顺序
     */
    private Integer questionIndex;

    /**
     * 生成时间 - 子问题生成时间
     */
    private LocalDateTime generatedAt;

    /**
     * 子问题答案 - 针对该子问题的具体回答
     */
    private String answer;

    // 默认构造函数
    public SubQuestion() {
    }

    // 带参构造函数
    public SubQuestion(String id, String userQueryId, String questionText, Integer questionIndex, LocalDateTime generatedAt, String answer) {
        this.id = id;
        this.userQueryId = userQueryId;
        this.questionText = questionText;
        this.questionIndex = questionIndex;
        this.generatedAt = generatedAt;
        this.answer = answer;
    }

    // Getter 和 Setter 方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserQueryId() {
        return userQueryId;
    }

    public void setUserQueryId(String userQueryId) {
        this.userQueryId = userQueryId;
    }

    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public Integer getQuestionIndex() {
        return questionIndex;
    }

    public void setQuestionIndex(Integer questionIndex) {
        this.questionIndex = questionIndex;
    }

    public LocalDateTime getGeneratedAt() {
        return generatedAt;
    }

    public void setGeneratedAt(LocalDateTime generatedAt) {
        this.generatedAt = generatedAt;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    @Override
    public String toString() {
        return "SubQuestion{" +
                "id='" + id + '\'' +
                ", userQueryId='" + userQueryId + '\'' +
                ", questionText='" + questionText + '\'' +
                ", questionIndex=" + questionIndex +
                ", generatedAt=" + generatedAt +
                ", answer='" + answer + '\'' +
                '}';
    }
}
