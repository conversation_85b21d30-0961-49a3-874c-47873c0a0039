package com.icarus.cmtt.search.entity;

import java.util.List;

public class SubQuestion {
    private final String question;
    private final String type;
    private final int priority;
    private final List<String> keywords;

    public SubQuestion(String question, String type, int priority, List<String> keywords) {
        this.question = question;
        this.type = type;
        this.priority = priority;
        this.keywords = keywords;
    }

    public String getQuestion() { return question; }
    public String getType() { return type; }
    public int getPriority() { return priority; }
    public List<String> getKeywords() { return keywords; }

    @Override
    public String toString() {
        return String.format("SubQuestion{question='%s', type='%s', priority=%d, keywords=%s}",
                question, type, priority, keywords);
    }
}
