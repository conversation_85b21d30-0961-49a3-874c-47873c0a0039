package com.icarus.cmtt.search.conversation.rag.query.content.aggregator;

import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.query.Query;

import java.util.Collection;
import java.util.List;
import java.util.Map;


public interface ContentAggregator {

    List<Content> aggregate(Map<Query, Collection<List<Content>>> queryToContents);
}
