package com.icarus.cmtt.search.conversation.rag.chain;

public class CmttConversationalRAGChain {
    private final ChatModel chatModel;
    private final ChatMemory chatMemory;
    private final RetrievalAugmentor retrievalAugmentor;

    public CmttConversationalRAGChain(ChatModel chatModel,
                                      ChatMemory chatMemory,
                                      RetrievalAugmentor retrievalAugmentor) {
        this.chatModel = chatModel;
        this.chatMemory = chatMemory != null ? chatMemory :
                MessageWindowChatMemory.withMaxMessages(10);
        this.retrievalAugmentor = retrievalAugmentor;
    }

    public String execute(String query) {
        UserMessage userMessage = UserMessage.from(query);
        userMessage = augment(userMessage);
        chatMemory.add(userMessage);

        AiMessage aiMessage = chatModel.chat(chatMemory.messages()).aiMessage();

        chatMemory.add(aiMessage);
        return aiMessage.text();
    }

    private UserMessage augment(UserMessage userMessage) {
        Metadata metadata = Metadata.from(userMessage, chatMemory.id(), chatMemory.messages());
        AugmentationRequest request = new AugmentationRequest(userMessage, metadata);
        AugmentationResult result = retrievalAugmentor.augment(request);
        return (UserMessage) result.chatMessage();
    }
}
