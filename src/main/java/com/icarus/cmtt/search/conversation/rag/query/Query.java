package com.icarus.cmtt.search.conversation.rag.query;

import java.util.Objects;


public class Query {

    private final String text;
    private final Metadata metadata;

    public Query(String text) {
        this.text = ensureNotBlank(text, "text");
        this.metadata = null;
    }

    public Query(String text, Metadata metadata) {
        this.text = ensureNotBlank(text, "text");
        this.metadata = ensureNotNull(metadata, "metadata");
    }

    public String text() {
        return text;
    }

    public Metadata metadata() {
        return metadata;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Query that = (Query) o;
        return Objects.equals(this.text, that.text)
                && Objects.equals(this.metadata, that.metadata);
    }

    @Override
    public int hashCode() {
        return Objects.hash(text, metadata);
    }

    @Override
    public String toString() {
        return "Query {" +
                " text = " + quoted(text) +
                ", metadata = " + metadata +
                " }";
    }

    public static Query from(String text) {
        return new Query(text);
    }

    public static Query from(String text, Metadata metadata) {
        return new Query(text, metadata);
    }
}
