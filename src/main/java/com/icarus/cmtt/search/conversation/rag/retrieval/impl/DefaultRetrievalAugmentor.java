package com.icarus.cmtt.search.conversation.rag.retrieval.impl;

import com.icarus.cmtt.search.conversation.rag.retrieval.RetrievalAugmentor;
import com.icarus.cmtt.search.conversation.rag.retrieval.components.transformer.QueryTransformer;

import java.util.List;
import java.util.concurrent.Executor;

public class DefaultRetrievalAugmentor implements RetrievalAugmentor {

    private final QueryTransformer queryTransformer;
    private final QueryRouter queryRouter;
    private final ContentAggregator contentAggregator;
    private final ContentInjector contentInjector;
    private final Executor executor;

    @Override
    public List<String> augment(String query) {
        return null;
    }
}
