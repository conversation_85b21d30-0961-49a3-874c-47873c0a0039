package com.icarus.cmtt.search.conversation.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icarus.cmtt.mapper.QuestionHistoryMapper;
import com.icarus.cmtt.search.conversation.service.ConversationService;
import com.icarus.cmtt.search.conversation.service.impl.langchain4j.CmttChatLanguageModel;
import com.icarus.cmtt.search.conversation.service.impl.langchain4j.CmttContentRetriever;
import com.icarus.cmtt.search.entity.QuestionHistory;
import dev.langchain4j.chain.ConversationalRetrievalChain;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.rag.DefaultRetrievalAugmentor;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service
public class ConversationServiceimpl implements ConversationService {

    @Resource
    private QuestionHistoryMapper questionHistoryMapper;

    @Autowired
    private CmttContentRetriever contentRetriever;


    // 为每个session维护独立的ChatMemory
    private final ConcurrentHashMap<String, ChatMemory> sessionChatMemories = new ConcurrentHashMap<>();

    @Override
    public QuestionHistory save(QuestionHistory questionHistory) {
        questionHistoryMapper.insert(questionHistory);
        return questionHistory ;
    }

    @Override
    public List<QuestionHistory> getAllHistory(String sessionId) {

        return questionHistoryMapper
                .selectList(new QueryWrapper<QuestionHistory>().eq("session_id", sessionId));
    }

    @Override
    public List<QuestionHistory> getHignQualityHistory(List<String> questionIds, Integer score) {
        List<QuestionHistory> questionHistories = questionHistoryMapper
                .selectList(new QueryWrapper<QuestionHistory>().in("id", questionIds));
        List<QuestionHistory> collect = questionHistories.stream()
                .sorted((q1, q2) -> {
                    // 获取人工评分和AI评分
                    String humanRating1 = q1.getRatingStar() != null ? q1.getRatingStar() : "0";
                    String humanRating2 = q2.getRatingStar() != null ? q2.getRatingStar() : "0";
                    String aiRating1 = q1.getAiRatingStar() != null ? q1.getAiRatingStar() : "0";
                    String aiRating2 = q2.getAiRatingStar() != null ? q2.getAiRatingStar() : "0";

                    try {
                        // 转换为数值进行计算
                        double hr1 = Double.parseDouble(humanRating1);
                        double hr2 = Double.parseDouble(humanRating2);
                        double air1 = Double.parseDouble(aiRating1);
                        double air2 = Double.parseDouble(aiRating2);

                        // 计算综合评分 (人工评分占70%，AI评分占30%)
                        double score1 = (StrUtil.equals(q1.getRatingStatus(), "0")) ? air1 : (hr1 * 0.7 + air1 * 0.3);
                        double score2 = (StrUtil.equals(q2.getRatingStatus(), "0")) ? air2 : (hr2 * 0.7 + air2 * 0.3);

                        // 从高到低排序
                        return Double.compare(score2, score1);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，按原始方式排序
                        return Double.compare(Double.parseDouble(aiRating2), Double.parseDouble(aiRating1));
                    }
                })
                .filter(q -> {
                    // 过滤出综合评分大于4的记录
                    String humanRating = q.getRatingStar() != null ? q.getRatingStar() : "0";
                    String aiRating = q.getAiRatingStar() != null ? q.getAiRatingStar() : "0";

                    try {
                        double hr = Double.parseDouble(humanRating);
                        double air = Double.parseDouble(aiRating);
                        double value = (StrUtil.equals(q.getRatingStatus(), "0")) ? air : (hr * 0.7 + air * 0.3);
                        return value > score;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    public String answer(String question, String sessionId) {
        // 为每个session获取或创建独立的ChatMemory
        ChatMemory chatMemory = sessionChatMemories.computeIfAbsent(sessionId,
                id -> MessageWindowChatMemory.withMaxMessages(10));

        DefaultRetrievalAugmentor defaultRetrievalAugmentor = DefaultRetrievalAugmentor.builder()
                .contentRetriever(contentRetriever)
                .build();

        ConversationalRetrievalChain chain = ConversationalRetrievalChain.builder()
                .chatLanguageModel(chatModel)
                .chatMemory(chatMemory)
                .retrievalAugmentor(defaultRetrievalAugmentor)
                .build();

        return chain.execute(question);
    }
}
