package com.icarus.cmtt.search.conversation.rag.chain.query;

public class Query {
    private final String text;
    private final Metadata metadata;

    public Query(String text) {
        this.text = text;
        this.metadata = null;
    }

    public Query(String text, Metadata metadata) {
        this.text = text;
        this.metadata = metadata;
    }

    public String text() {
        return text;
    }

    public Metadata metadata() {
        return metadata;
    }


    public static Query from(String text) {
        return new Query(text);
    }

    public static Query from(String text, Metadata metadata) {
        return new Query(text, metadata);
    }
}
