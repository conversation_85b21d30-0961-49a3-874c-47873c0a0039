package com.icarus.cmtt.search.conversation.rag.chain.components.router.impl;

import com.icarus.cmtt.search.conversation.rag.chain.components.retrieval.ContentRetriever;
import com.icarus.cmtt.search.conversation.rag.chain.components.router.QueryRouter;

import java.util.Collection;

import static com.icarus.cmtt.util.ValidationUtil.ensureNotEmpty;
import static java.util.Arrays.asList;
import static java.util.Collections.unmodifiableCollection;

public class DefaultQueryRouter implements QueryRouter {

    private final Collection<ContentRetriever> contentRetrievers;

    public DefaultQueryRouter(ContentRetriever... contentRetrievers) {
        this(asList(contentRetrievers));
    }

    public DefaultQueryRouter(Collection<ContentRetriever> contentRetrievers) {
        this.contentRetrievers = unmodifiableCollection(ensureNotEmpty(contentRetrievers, "contentRetrievers"));
    }

    @Override
    public Collection<ContentRetriever> route(String query) {
        return contentRetrievers;
    }
}
