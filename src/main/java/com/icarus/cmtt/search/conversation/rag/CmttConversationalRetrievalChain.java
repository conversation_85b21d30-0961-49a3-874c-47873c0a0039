package com.icarus.cmtt.search.conversation.rag;

import com.icarus.cmtt.search.conversation.rag.chain.RetrievalAugmentor;
import com.icarus.cmtt.search.conversation.rag.chain.components.retrieval.ContentRetriever;
import com.icarus.cmtt.search.conversation.rag.chain.impl.DefaultRetrievalAugmentor;
import com.icarus.sdk.client.utils.LLMClientUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class CmttConversationalRetrievalChain {

    private final RetrievalAugmentor retrievalAugmentor;
    private final LLMClientUtil llmClientUtil;

    public CmttConversationalRetrievalChain(RetrievalAugmentor retrievalAugmentor, LLMClientUtil llmClientUtil) {
        this.retrievalAugmentor = retrievalAugmentor;
        this.llmClientUtil = llmClientUtil;
    }

    public String execute(String query) {

        List<String> contentList = augment(query);
        String content = StringUtils.join(contentList, "\n");
        String anwser = llmClientUtil.builder().systemPrompt(content).chat(query).runChatCompletion();

        return anwser;
    }

    private List<String> augment(String query) {

        List<String> augmentationResult = retrievalAugmentor.augment(query);

        return augmentationResult;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private LLMClientUtil llmClientUtil;
        private RetrievalAugmentor retrievalAugmentor;

        public Builder llmClientUtil(LLMClientUtil llmClientUtil) {
            this.llmClientUtil = llmClientUtil;
            return this;
        }

        public Builder contentRetriever(ContentRetriever contentRetriever) {
            if (contentRetriever != null) {
                this.retrievalAugmentor = DefaultRetrievalAugmentor.builder()
                        .contentRetriever(contentRetriever)
                        .build();
            }
            return this;
        }

        public Builder retrievalAugmentor(RetrievalAugmentor retrievalAugmentor) {
            this.retrievalAugmentor = retrievalAugmentor;
            return this;
        }

        public CmttConversationalRetrievalChain build() {
            return new CmttConversationalRetrievalChain(retrievalAugmentor, llmClientUtil);
        }
    }
}
