package com.icarus.cmtt.search.conversation.rag.chain.components.aggregator;

import java.util.*;

import static com.icarus.cmtt.util.ValidationUtil.ensureBetween;

public class ReciprocalRankFuser {
    public static List<String> fuse(Collection<List<String>> listsOfContents) {
        return fuse(listsOfContents, 60);
    }

    public static List<String> fuse(Collection<List<String>> listsOfContents, int k) {
        ensureBetween(k, 1, Integer.MAX_VALUE, "k");

        Map<String, Double> scores = new LinkedHashMap<>();
        for (List<String> singleListOfContent : listsOfContents) {
            for (int i = 0; i < singleListOfContent.size(); i++) {
                String content = singleListOfContent.get(i);
                double currentScore = scores.getOrDefault(content, 0.0);
                int rank = i + 1;
                double newScore = currentScore + 1.0 / (k + rank);
                scores.put(content, newScore);
            }
        }

        List<String> fused = new ArrayList<>(scores.keySet());
        fused.sort(Comparator.comparingDouble(scores::get).reversed());
        return fused;
    }

}
