package com.icarus.cmtt.search.conversation.rag.query.content;

import java.util.Map;

public interface Content {

    TextSegment textSegment();

    Map<ContentMetadata, Object> metadata();

    static Content from(String text) {
        return new DefaultContent(text);
    }

    static Content from(TextSegment textSegment) {
        return new DefaultContent(textSegment);
    }

    static Content from(TextSegment textSegment, Map<ContentMetadata, Object> metadata) {
        return new DefaultContent(textSegment, metadata);
    }
}
