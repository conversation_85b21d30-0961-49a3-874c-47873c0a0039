package dev.langchain4j.data.message;

import dev.langchain4j.Experimental;
import dev.langchain4j.agent.tool.ToolExecutionRequest;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static dev.langchain4j.data.message.ChatMessageType.AI;
import static dev.langchain4j.internal.Utils.copy;
import static dev.langchain4j.internal.Utils.isNullOrEmpty;
import static dev.langchain4j.internal.Utils.quoted;
import static dev.langchain4j.internal.ValidationUtils.ensureNotEmpty;
import static dev.langchain4j.internal.ValidationUtils.ensureNotNull;
import static java.util.Arrays.asList;

/**
 * Represents a message generated by AI (language model).
 * This message can contain:
 * <pre>
 * - {@link #text()}: textual content
 * - {@link #thinking()}: thinking/reasoning content
 * - {@link #toolExecutionRequests()}: requests to execute tools
 * - {@link #attributes()}: additional attributes, typically provider-specific
 * </pre>
 * <p>
 * In case this message contains tool execution requests,
 * the response to this message should be one {@link ToolExecutionResultMessage} for each tool execution request.
 */
public class AiMessage implements ChatMessage {

    private final String text;
    private final String thinking;
    private final List<ToolExecutionRequest> toolExecutionRequests;
    private final Map<String, Object> attributes;

    /**
     * Create a new {@link AiMessage} with the given text.
     *
     * @param text the text of the message.
     */
    public AiMessage(String text) {
        this.text = ensureNotNull(text, "text");
        this.thinking = null;
        this.toolExecutionRequests = List.of();
        this.attributes = Map.of();
    }

    /**
     * Create a new {@link AiMessage} with the given tool execution requests.
     *
     * @param toolExecutionRequests the tool execution requests of the message.
     */
    public AiMessage(List<ToolExecutionRequest> toolExecutionRequests) {
        this.text = null;
        this.thinking = null;
        this.toolExecutionRequests = ensureNotEmpty(toolExecutionRequests, "toolExecutionRequests");
        this.attributes = Map.of();
    }

    /**
     * Create a new {@link AiMessage} with the given text and tool execution requests.
     *
     * @param text                  the text of the message.
     * @param toolExecutionRequests the tool execution requests of the message.
     */
    public AiMessage(String text, List<ToolExecutionRequest> toolExecutionRequests) {
        this.text = text;
        this.thinking = null;
        this.toolExecutionRequests = copy(toolExecutionRequests);
        this.attributes = Map.of();
    }

    /**
     * @since 1.2.0
     */
    public AiMessage(Builder builder) {
        this.text = builder.text;
        this.thinking = builder.thinking;
        this.toolExecutionRequests = copy(builder.toolExecutionRequests);
        this.attributes = copy(builder.attributes);
    }

    /**
     * Get the text of the message.
     *
     * @return the text of the message.
     */
    public String text() {
        return text;
    }

    /**
     * Get the thinking/reasoning text of the message.
     *
     * @return the thinking/reasoning text of the message.
     * @since 1.2.0
     */
    @Experimental
    public String thinking() {
        return thinking;
    }

    /**
     * Get the tool execution requests of the message.
     *
     * @return the tool execution requests of the message.
     */
    public List<ToolExecutionRequest> toolExecutionRequests() {
        return toolExecutionRequests;
    }

    /**
     * Check if the message has {@link ToolExecutionRequest}s.
     *
     * @return true if the message has {@link ToolExecutionRequest}s, false otherwise.
     */
    public boolean hasToolExecutionRequests() {
        return !isNullOrEmpty(toolExecutionRequests);
    }

    /**
     * Returns additional attributes, typically provider-specific.
     *
     * @see #attribute(String, Class)
     * @since 1.2.0
     */
    @Experimental
    public Map<String, Object> attributes() {
        return attributes;
    }

    /**
     * Returns additional attribute by it's key.
     *
     * @see #attributes()
     * @since 1.2.0
     */
    @Experimental
    public <T> T attribute(String key, Class<T> type) {
        return (T) attributes.get(key);
    }

    @Override
    public ChatMessageType type() {
        return AI;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AiMessage that = (AiMessage) o;
        return Objects.equals(this.text, that.text)
                && Objects.equals(this.thinking, that.thinking)
                && Objects.equals(this.toolExecutionRequests, that.toolExecutionRequests)
                && Objects.equals(this.attributes, that.attributes);
    }

    @Override
    public int hashCode() {
        return Objects.hash(text, thinking, toolExecutionRequests, attributes);
    }

    @Override
    public String toString() {
        return "AiMessage {" +
                " text = " + quoted(text) +
                ", thinking = " + quoted(thinking) +
                ", toolExecutionRequests = " + toolExecutionRequests +
                ", attributes = " + attributes +
                " }";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String text;
        private String thinking;
        private List<ToolExecutionRequest> toolExecutionRequests;
        private Map<String, Object> attributes;

        public Builder text(String text) {
            this.text = text;
            return this;
        }

        /**
         * @since 1.2.0
         */
        @Experimental
        public Builder thinking(String thinking) {
            this.thinking = thinking;
            return this;
        }

        public Builder toolExecutionRequests(List<ToolExecutionRequest> toolExecutionRequests) {
            this.toolExecutionRequests = toolExecutionRequests;
            return this;
        }

        /**
         * @since 1.2.0
         */
        @Experimental
        public Builder attributes(Map<String, Object> attributes) {
            this.attributes = attributes;
            return this;
        }

        public AiMessage build() {
            return new AiMessage(this);
        }
    }

    /**
     * Create a new {@link AiMessage} with the given text.
     *
     * @param text the text of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage from(String text) {
        return new AiMessage(text);
    }

    /**
     * Create a new {@link AiMessage} with the given tool execution requests.
     *
     * @param toolExecutionRequests the tool execution requests of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage from(ToolExecutionRequest... toolExecutionRequests) {
        return from(asList(toolExecutionRequests));
    }

    /**
     * Create a new {@link AiMessage} with the given tool execution requests.
     *
     * @param toolExecutionRequests the tool execution requests of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage from(List<ToolExecutionRequest> toolExecutionRequests) {
        return new AiMessage(toolExecutionRequests);
    }

    /**
     * Create a new {@link AiMessage} with the given text and tool execution requests.
     *
     * @param text                  the text of the message.
     * @param toolExecutionRequests the tool execution requests of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage from(String text, List<ToolExecutionRequest> toolExecutionRequests) {
        return new AiMessage(text, toolExecutionRequests);
    }

    /**
     * Create a new {@link AiMessage} with the given text.
     *
     * @param text the text of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage aiMessage(String text) {
        return from(text);
    }

    /**
     * Create a new {@link AiMessage} with the given tool execution requests.
     *
     * @param toolExecutionRequests the tool execution requests of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage aiMessage(ToolExecutionRequest... toolExecutionRequests) {
        return aiMessage(asList(toolExecutionRequests));
    }

    /**
     * Create a new {@link AiMessage} with the given tool execution requests.
     *
     * @param toolExecutionRequests the tool execution requests of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage aiMessage(List<ToolExecutionRequest> toolExecutionRequests) {
        return from(toolExecutionRequests);
    }

    /**
     * Create a new {@link AiMessage} with the given text and tool execution requests.
     *
     * @param text                  the text of the message.
     * @param toolExecutionRequests the tool execution requests of the message.
     * @return the new {@link AiMessage}.
     */
    public static AiMessage aiMessage(String text, List<ToolExecutionRequest> toolExecutionRequests) {
        return from(text, toolExecutionRequests);
    }
}
