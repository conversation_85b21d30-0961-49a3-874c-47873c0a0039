package com.icarus.cmtt.search.conversation.rag.chain.impl;

import com.icarus.cmtt.search.conversation.rag.chain.RetrievalAugmentor;
import com.icarus.cmtt.search.conversation.rag.chain.components.aggregator.ContentAggregator;
import com.icarus.cmtt.search.conversation.rag.chain.components.retrieval.ContentRetriever;
import com.icarus.cmtt.search.conversation.rag.chain.components.router.QueryRouter;
import com.icarus.cmtt.search.conversation.rag.chain.components.router.impl.DefaultQueryRouter;
import com.icarus.cmtt.search.conversation.rag.chain.components.transformer.QueryTransformer;
import com.icarus.cmtt.search.conversation.rag.chain.components.transformer.impl.DefaultQueryTransformer;
import com.icarus.cmtt.search.conversation.rag.chain.components.aggregator.impl.DefaultContentAggregator;
import com.icarus.cmtt.search.conversation.rag.chain.query.Query;
import com.icarus.cmtt.util.ValidationUtil;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static java.util.Collections.*;
import static java.util.Collections.emptyMap;
import static java.util.concurrent.CompletableFuture.allOf;
import static java.util.concurrent.CompletableFuture.supplyAsync;
import static java.util.concurrent.TimeUnit.SECONDS;
import static java.util.stream.Collectors.toMap;

public class DefaultRetrievalAugmentor implements RetrievalAugmentor {

    private final QueryTransformer queryTransformer;
    private final QueryRouter queryRouter;
    private final ContentAggregator contentAggregator;
    private final Executor executor;

    public DefaultRetrievalAugmentor(QueryTransformer queryTransformer,
                                     QueryRouter queryRouter,
                                     ContentAggregator contentAggregator,
                                     Executor executor) {
        this.queryTransformer = getOrDefault(queryTransformer, DefaultQueryTransformer::new);
        this.queryRouter = ValidationUtil.ensureNotNull(queryRouter, "queryRouter");
        this.contentAggregator = getOrDefault(contentAggregator, DefaultContentAggregator::new);
        this.executor = getOrDefault(executor, DefaultRetrievalAugmentor::createDefaultExecutor);
    }

    private static ExecutorService createDefaultExecutor() {
        return new ThreadPoolExecutor(
                0, Integer.MAX_VALUE,
                1, SECONDS,
                new SynchronousQueue<>()
        );
    }
    @Override
    public List<String> augment(String originalQuery) {
        Query query = Query.from(originalQuery);
        Collection<String> queries = queryTransformer.transform(originalQuery);

        Map<String, Collection<List<String>>> queryToContents = process(queries);

        List<String> aggregate = contentAggregator.aggregate(queryToContents);

        return aggregate;
    }

    private Map<String, Collection<List<String>>> process(Collection<String> queries) {
        if (queries.size() == 1) {
            String query = queries.iterator().next();
            Collection<ContentRetriever> retrievers = queryRouter.route(query);
            if (retrievers.size() == 1) {
                ContentRetriever contentRetriever = retrievers.iterator().next();
                List<String> contents = contentRetriever.retrieve(query);
                return singletonMap(query, singletonList(contents));
            } else if (retrievers.size() > 1) {
                Collection<List<String>> contents = retrieveFromAll(retrievers, query).join();
                return singletonMap(query, contents);
            } else {
                return emptyMap();
            }
        } else if (queries.size() > 1) {
            Map<String, CompletableFuture<Collection<List<String>>>> queryToFutureContents = new ConcurrentHashMap<>();
            queries.forEach(query -> {
                CompletableFuture<Collection<List<String>>> futureContents =
                        supplyAsync(() -> queryRouter.route(query), executor)
                                .thenCompose(retrievers -> retrieveFromAll(retrievers, query));
                queryToFutureContents.put(query, futureContents);
            });
            return join(queryToFutureContents);
        } else {
            return emptyMap();
        }
    }

    private CompletableFuture<Collection<List<String>>> retrieveFromAll(Collection<ContentRetriever> retrievers,
                                                                         String query) {
        List<CompletableFuture<List<String>>> futureContents = retrievers.stream()
                .map(retriever -> supplyAsync(() -> retriever.retrieve(query), executor))
                .collect(Collectors.toList());

        return allOf(futureContents.toArray(new CompletableFuture[0]))
                .thenApply(ignored ->
                        futureContents.stream()
                                .map(CompletableFuture::join)
                                .collect(Collectors.toList()));
    }

    private static Map<String, Collection<List<String>>> join(
            Map<String, CompletableFuture<Collection<List<String>>>> queryToFutureContents) {
        return allOf(queryToFutureContents.values().toArray(new CompletableFuture[0]))
                .thenApply(ignored ->
                        queryToFutureContents.entrySet().stream()
                                .collect(toMap(
                                        Map.Entry::getKey,
                                        entry -> entry.getValue().join()
                                ))
                ).join();
    }


    public static DefaultRetrievalAugmentorBuilder builder() {
        return new DefaultRetrievalAugmentorBuilder();
    }

    public static class DefaultRetrievalAugmentorBuilder {

        private QueryTransformer queryTransformer;
        private QueryRouter queryRouter;
        private ContentAggregator contentAggregator;
        private Executor executor;

        DefaultRetrievalAugmentorBuilder() {
        }

        public DefaultRetrievalAugmentorBuilder contentRetriever(ContentRetriever contentRetriever) {
            this.queryRouter = new DefaultQueryRouter(ValidationUtil.ensureNotNull(contentRetriever, "contentRetriever"));
            return this;
        }

        public DefaultRetrievalAugmentorBuilder queryTransformer(QueryTransformer queryTransformer) {
            this.queryTransformer = queryTransformer;
            return this;
        }

        public DefaultRetrievalAugmentorBuilder queryRouter(QueryRouter queryRouter) {
            this.queryRouter = queryRouter;
            return this;
        }

        public DefaultRetrievalAugmentorBuilder contentAggregator(ContentAggregator contentAggregator) {
            this.contentAggregator = contentAggregator;
            return this;
        }

        public DefaultRetrievalAugmentorBuilder executor(Executor executor) {
            this.executor = executor;
            return this;
        }

        public DefaultRetrievalAugmentor build() {
            return new DefaultRetrievalAugmentor(this.queryTransformer, this.queryRouter, this.contentAggregator, this.executor);
        }
    }

    public static <T> T getOrDefault(T value, Supplier<T> defaultValueSupplier) {
        return value != null ? value : defaultValueSupplier.get();
    }
}
