package com.icarus.cmtt.search.conversation.rag.chain.components.transformer.impl;

import cn.hutool.core.util.StrUtil;
import com.icarus.cmtt.search.conversation.rag.chain.components.transformer.QueryTransformer;
import com.icarus.sdk.client.utils.LLMClientUtil;

import java.util.Collection;
import java.util.List;

import static java.util.Arrays.stream;
import static java.util.stream.Collectors.toList;

public class DefaultQueryTransformer implements QueryTransformer {

    private LLMClientUtil llmClientUtil;

    @Override
    public Collection<String> transform(String query) {
        String prompt = createPrompt(query);
        String response = llmClientUtil.builder().modelName("").chat(prompt).runChatCompletion();
        List<String> queries = parse(response);

        return queries;
    }
    private List<String> parse(String queries) {
        return stream(queries.split("\n"))
                .filter(StrUtil::isNotBlank)
                .collect(toList());
    }

    // TODO: 创建prompt，重组问题
    private String createPrompt(String query) {
        return "";
    }

}
