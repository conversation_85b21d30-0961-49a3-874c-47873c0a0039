package com.icarus.cmtt.search.conversation.rag.chain.message;

import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
public class UserMessage implements ChatMessage {

    private final String userId;
    private final String sessionId;
    private final String content;

    public UserMessage(String userId, String sessionId, String content) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.content = content;
    }

    @Override
    public String type() {
        return "user";
    }

    public static UserMessage from(String userId, String sessionId, String content) {
        return new UserMessage(userId, sessionId, content);
    }
}
