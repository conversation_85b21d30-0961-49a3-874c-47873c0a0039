package com.icarus.cmtt.search.conversation.rag.chain.message;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class UserMessage implements ChatMessage{

    private String SessionId;

    private String originalQuery;

    private List<String> subQueries;

    private Map<String, List<String>> subQueryKnowledgebaseIds;

    private Map<String, List<String>> subQuery

    private String answer;
    @Override
    public String type() {
        return "user";
    }
}
