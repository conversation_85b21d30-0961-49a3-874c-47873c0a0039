package com.icarus.cmtt.search.conversation.rag.chain.components.retrieval.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.cmtt.enums.AipApiEnum;
import com.icarus.cmtt.search.conversation.rag.chain.components.retrieval.ContentRetriever;
import com.icarus.cmtt.service.ICorpusScopeRuleService;
import com.icarus.cmtt.util.AipApiClientUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CmttContentRetriever implements ContentRetriever {

    @Autowired
    private ICorpusScopeRuleService corpusScopeRuleService;

    @Override
    public List<String> retrieve(String query) {

        JSONObject findTagBodyJson = new JSONObject();
        findTagBodyJson.put("query", query);

        vectorParam tagVectorParam = new vectorParam()
                .setOperationType("VECTOR_RETRIEVE")
                .setOperationObject("TAG")
                .setBodyJson(JSONUtil.toJsonStr(findTagBodyJson));

        //根据向量相似度获取标签
        String s = AipApiClientUtil.callApi(AipApiEnum.vector.getPath(), tagVectorParam);

        JSONObject entries = AipApiClientUtil.parseAipResult(s);

        String[] tags = new String[]{};

        //根据标签获取知识库id
        List<String> sortSystemIdFromTags = corpusScopeRuleService.getSortSystemIdFromTags(tags, 10);

        JSONObject findChunkBodyJson = new JSONObject();
        findChunkBodyJson.put("query", query);
        findChunkBodyJson.put("limit", 10);
        findChunkBodyJson.put("threshold", 0.7);
        findChunkBodyJson.put("baseId", sortSystemIdFromTags);
        findChunkBodyJson.put("appName", "cmtt");

        vectorParam chunckVectorParam = new vectorParam()
                .setOperationType("VECTOR_RETRIEVE")
                .setOperationObject("TAG")
                .setBodyJson(JSONUtil.toJsonStr(findTagBodyJson));

        String chuncks = AipApiClientUtil.callApi(AipApiEnum.vector.getPath(), chunckVectorParam);

        //根据向量相似度获取切片 内容
        JSONObject content = AipApiClientUtil.parseAipResult(chuncks);


        return new ArrayList<>();
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class vectorParam{
        private String operationType;
        private String operationObject;
        private String bodyJson;
    }

}
