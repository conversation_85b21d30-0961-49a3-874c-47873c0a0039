package com.icarus.cmtt.search.conversation.rag.chain.components.aggregator.impl;

import com.icarus.cmtt.search.conversation.rag.chain.components.aggregator.ContentAggregator;
import com.icarus.cmtt.search.conversation.rag.chain.components.aggregator.ReciprocalRankFuser;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class DefaultContentAggregator implements ContentAggregator {
    @Override
    public List<String> aggregate(Map<String, Collection<List<String>>> queryToContents) {
        Map<String, List<String>> fused = fuse(queryToContents);

        return ReciprocalRankFuser.fuse(fused.values());
    }

    /**
     *
     * @param queryToContents
     * @return
     */
    protected Map<String, List<String>> fuse(Map<String, Collection<List<String>>> queryToContents) {
        Map<String, List<String>> fused = new LinkedHashMap<>();
        for (String query : queryToContents.keySet()) {
            Collection<List<String>> contents = queryToContents.get(query);
            fused.put(query, ReciprocalRankFuser.fuse(contents));
        }
        return fused;
    }

}
