package com.icarus.cmtt.search.conversation.controller;

import com.icarus.cmtt.search.conversation.service.ConversationService;
import com.icarus.cmtt.search.entity.QuestionHistory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class ConversationController {

    @Autowired
    private ConversationService conversationService;

    @GetMapping("/getAllHistory")
    public List<QuestionHistory> getAllHistory(String sessionId) {
       return conversationService.getAllHistory(sessionId);
    }

    @PostMapping("/save")
    public QuestionHistory save(QuestionHistory questionHistory){
        return conversationService.save(questionHistory);
    }

    @GetMapping("/getHignQualityHistory")
    public List<QuestionHistory> getHignQualityHistory(List<String> questionIds, Integer score) {
       return conversationService.getHignQualityHistory(questionIds,  score);
    }

    @GetMapping("/answer")
    public String answer(String question, String sessionId, String modelName) {
        return conversationService.answer(question, sessionId);
    }
}
