package com.icarus.cmtt.mapper;

import com.icarus.cmtt.entity.CorpusDatabase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Mapper
public interface CorpusDatabaseMapper extends BaseMapper<CorpusDatabase> {
    @Select("select * from corpus_database where name = #{s} and parent_id = #{parentId}")
    CorpusDatabase findByNameAndParentId(@Param("s") String s,@Param("parentId") String parentId);


    List<String> getNodeId(String id);

    List<String> getFullPath(String id);
}
