package com.icarus.cmtt.mapper;

import com.icarus.cmtt.entity.CorpusScopeRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface CorpusScopeRuleMapper extends BaseMapper<CorpusScopeRule> {


    @Select({
            "<script>",
            "SELECT DISTINCT system_id",
            "FROM corpus_scope_rule",
            "WHERE EXISTS (",
            "    SELECT 1",
            "    FROM unnest(#{tags}::text[]) AS tag",
            "    WHERE tag = ANY(string_to_array(value, ','))",
            ")",
            "</script>"
    })
    List<String> getSystemIdFromTags(@Param("tags")String[] tags);
}
