package com.icarus.cmtt.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icarus.cmtt.dto.KnowledgebaseMappingDTO;
import com.icarus.cmtt.entity.KnowledgebaseMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.vo.KnowledgebaseTagsVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface KnowledgebaseMappingMapper extends BaseMapper<KnowledgebaseMapping> {

    IPage<KnowledgebaseTagsVo> selectKnowledgebaseTagsPage(
            IPage<KnowledgebaseTagsVo> page,
            @Param("dto") KnowledgebaseMappingDTO queryDTO
    );}

