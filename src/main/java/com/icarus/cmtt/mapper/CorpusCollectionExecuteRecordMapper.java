package com.icarus.cmtt.mapper;

import com.icarus.cmtt.entity.CorpusCollectionExecuteRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Mapper
public interface CorpusCollectionExecuteRecordMapper extends BaseMapper<CorpusCollectionExecuteRecord> {

    /**
     * 根据批次ID查询执行记录
     * @param batchId 批次ID
     * @return 执行记录列表
     */
    List<CorpusCollectionExecuteRecord> findByBatchId(@Param("batchId") String batchId);

    /**
     * 根据语料收集配置ID和批次ID更新状态
     * @param corpusCollectionConfigId 语料收集配置ID
     * @param batchId 批次ID
     * @return 影响行数
     */
    int updateByCorpusCollectionConfigIdAndBatchId(@Param("corpusCollectionConfigId") String corpusCollectionConfigId, @Param("batchId") String batchId);

}
