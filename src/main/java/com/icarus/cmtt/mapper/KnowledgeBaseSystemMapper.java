package com.icarus.cmtt.mapper;

import com.icarus.cmtt.entity.KnowledgeBaseSystem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface KnowledgeBaseSystemMapper extends BaseMapper<KnowledgeBaseSystem> {

    List<String> getNodes(String id);

    List<String> getFullPath(String id);
}
