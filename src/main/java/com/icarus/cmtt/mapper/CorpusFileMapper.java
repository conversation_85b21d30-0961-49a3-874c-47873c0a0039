package com.icarus.cmtt.mapper;

import com.icarus.cmtt.entity.CorpusFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Mapper
public interface CorpusFileMapper extends BaseMapper<CorpusFile> {

    /**
     * 根据语料ID删除语料文件
     * @param corpusId 语料ID
     * @return 影响行数
     */
    int deleteByCorpusId(@Param("corpusId") String corpusId);

    /**
     * 根据语料ID查询语料文件列表
     * @param corpusId 语料ID
     * @return 语料文件列表
     */
    List<CorpusFile> findByCorpusId(@Param("corpusId") String corpusId);

}
