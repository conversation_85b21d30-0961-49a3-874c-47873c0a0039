package com.icarus.cmtt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.CorpusSliceSegment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MyBatis Plus Mapper - CorpusSliceSegment
 */
public interface CorpusSliceSegmentMapper extends BaseMapper<CorpusSliceSegment> {

    Integer getSumLengthByTags(@Param("list")List<String> tagIds);

    Integer getCountByTags(@Param("list")List<String> tagIds);

    List<String> getIdsByTags(@Param("list")List<String> tagIds);

}