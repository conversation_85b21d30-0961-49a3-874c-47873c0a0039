package com.icarus.cmtt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.CorpusCollectionConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Mapper
public interface CorpusCollectionConfigMapper extends BaseMapper<CorpusCollectionConfig> {

    List<String> selectIdsByFrequency(@Param("frequency") String frequency,
                                      @Param("startTime") String startTime,
                                      @Param("endTime") String endTime);

    /**
     * 根据收集类型查询语料收集配置
     * @param collectionType 收集类型
     * @return 语料收集配置列表
     */
    List<CorpusCollectionConfig> findByCollectionChannel(@Param("collectionType") String collectionType);

}
