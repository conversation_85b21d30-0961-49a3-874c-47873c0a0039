package com.icarus.cmtt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface KnowledgebaseMappingTagsMapper extends BaseMapper<KnowledgebaseMappingTags> {

    @Delete("DELETE FROM knowledge_base_mapping_tags WHERE knowledgebase_mapping_id = #{id}")
    Integer deleteByBaseId(Integer id);

    @Select("SELECT id,tag_id,knowledgebase_mapping_id,create_time,update_time FROM knowledge_base_mapping_tags WHERE knowledgebase_mapping_id = #{id}")
    List<KnowledgebaseMappingTags> findByBaseId(Integer id);
}
