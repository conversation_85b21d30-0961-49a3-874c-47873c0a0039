package com.icarus.cmtt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.CrawlContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Mapper
public interface CrawlContentMapper extends BaseMapper<CrawlContent> {

    /**
     * 根据爬虫设置ID和批次ID查询爬虫内容
     * @param crawlSettingId 爬虫设置ID
     * @param batchId 批次ID
     * @return 爬虫内容列表
     */
    List<CrawlContent> findByCrawlSettingIdAndCrawlBatchId(@Param("crawlSettingId") Integer crawlSettingId, @Param("batchId") String batchId);

}
