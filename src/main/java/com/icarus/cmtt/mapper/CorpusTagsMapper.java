package com.icarus.cmtt.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.CorpusTags;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CorpusTagsMapper extends BaseMapper<CorpusTags> {

	@Select("SELECT count(id) FROM public.related_corpus_tags WHERE tag_id = #{tagId}")
	Integer getCorpusCountByTagId(Integer tagId);

	@Select("SELECT count(id) FROM public.related_corpus_database_tags WHERE tag_id = #{tagId}")
	Integer getCorpusDataBaseCountByTagId(Integer tagId);


	@Select("SELECT \n" +
			"    distinct ct.name \n" +
			"FROM \n" +
			"    corpus_scope_rule csr\n" +
			"CROSS JOIN \n" +
			"    unnest(string_to_array(csr.value, ',')) AS split_tag_id\n" +
			"LEFT JOIN \n" +
			"    corpus_tags ct ON ct.id = split_tag_id::bigint\n" +
			"WHERE \n" +
			"    csr.system_id = #{id}")
    List<String> getTagNameBySystemId(String id);

	String getFullPathName(Integer id);
}
