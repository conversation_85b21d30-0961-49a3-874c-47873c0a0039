package com.icarus.cmtt.mapper;

import com.icarus.cmtt.entity.CorpusDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface CorpusDetailMapper extends BaseMapper<CorpusDetail> {

    /**
     * 根据爬虫内容ID查询语料详情
     * @param crawlContentId 爬虫内容ID
     * @return 语料详情
     */
    CorpusDetail findByCrawlContentId(@Param("crawlContentId") String crawlContentId);

    /**
     * 根据ID更新收集状态
     * @param id 语料详情ID
     * @param status 状态
     * @return 影响行数
     */
    int updateCollectionStatusById(@Param("id") String id, @Param("status") Integer status);

}
