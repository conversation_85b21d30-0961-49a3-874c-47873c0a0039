package com.icarus.cmtt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.CorpusSliceDocument;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * MyBatis Plus Mapper - CorpusSliceDocument
 */
@Mapper
public interface CorpusSliceDocumentMapper extends BaseMapper<CorpusSliceDocument> {
    List<String> getFileIdsBySystemId(String id);
}