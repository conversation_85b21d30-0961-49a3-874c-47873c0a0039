package com.icarus.cmtt.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CorpusSearchDTO {
    private List<DirectoryScope> directoryScopes;
    private List<Keyword> keywords;
    private BasicAttribute basicAttributes;
    private List<BusinessTag> businessTags;
    private Integer pageNo;
    private Integer pageSize;

    @Data
    public static class DirectoryScope {
        private MatchMode matchMode;
        private String corpusDirectory;
        private Condition condition;
    }

    @Data
    public static class Keyword {
        private MatchMode matchMode;
        private Scope scope;
        private Condition condition;
        private String keyword;
    }

    @Data
    public static class BasicAttribute {
        private List<PublishDate> publishDate;
        private List<PublishOrg> publishOrg;
        private String versionState;
    }

    @Data
    public static class BusinessTag {
        private MatchMode matchMode;
        private Condition condition;
        private String value;
    }

    @Data
    public static class PublishDate {
        private MatchMode matchMode;
        private Condition condition;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startDate;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endDate;
    }

    @Data
    public static class PublishOrg {
        private MatchMode matchMode;
        private Condition condition;
        private String orgCode;
    }

    @Getter
    public enum MatchMode {
        OR("or"), AND("and");
        private final String value;

        MatchMode(String value) {
            this.value = value;
        }

        @JsonCreator
        public static MatchMode parse(String str) {
            switch (str) {
                case "or":
                    return OR;
                case "and":
                    return AND;
                default:
                    return null;
            }
        }
    }

    @Getter
    public enum Condition {
        EQUAL("equal"), NOT_EQUAL("notEqual"), CONTAIN("contain"), NOT_CONTAIN("notContain");

        private final String value;

        Condition(String value) {
            this.value = value;
        }

        @JsonCreator
        public static Condition parse(String str) {
            switch (str) {
                case "equal":
                    return EQUAL;
                case "notEqual":
                    return NOT_EQUAL;
                case "contain":
                case "in":
                    return CONTAIN;
                case "notContain":
                    return NOT_CONTAIN;
                default:
                    return null;
            }
        }
    }

    @Getter
    public enum Scope {
        TITLE("title"), TEXT("text"), SUMMARY("summary"), KEYWORD("keyword");
        private final String value;

        Scope(String value) {
            this.value = value;
        }

        @JsonCreator
        public static Scope parse(String str) {
            switch (str) {
                case "title":
                    return TITLE;
                case "text":
                    return TEXT;
                case "summary":
                    return SUMMARY;
                case "keyword":
                    return KEYWORD;
                default:
                    return null;
            }
        }
    }
}
