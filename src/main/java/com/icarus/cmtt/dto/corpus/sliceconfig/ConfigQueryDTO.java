package com.icarus.cmtt.dto.corpus.sliceconfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ConfigQueryDTO {
    private Integer pageNo;
    private Integer pageSize;
    private SliceConfigQueryBody queryBody;

    @Data
    public static class SliceConfigQueryBody {
        private String id;
        private String name;
        private String cotId;
        private Integer status;
        private String description;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createStartTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createEndTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateStartTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateEndTime;
    }
}
