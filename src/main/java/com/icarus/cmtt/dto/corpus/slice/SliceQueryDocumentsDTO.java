package com.icarus.cmtt.dto.corpus.slice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SliceQueryDocumentsDTO {
    private Integer pageNo;
    private Integer pageSize;
    private DocumentQueryBody queryBody;

    @Data
    public static class DocumentQueryBody {
        private Long id;
        private Integer status;
        private String title;
        private Integer number;
        private String[] tags;
        private List<String> ids;
        private String importMethod;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createStartTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createEndTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateStartTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateEndTime;
        private String knowledgeBaseSystemId;
    }
}
