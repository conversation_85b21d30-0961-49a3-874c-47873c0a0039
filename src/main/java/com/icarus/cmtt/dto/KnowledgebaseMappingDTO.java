package com.icarus.cmtt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.entity.KnowledgebaseMapping;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class KnowledgebaseMappingDTO {

    // 主键
    private Integer id;

    // 知识库映射名称
    private String name;

    // 关联标签
    private List<Integer> relatedTags;

    // 创建人
    private String created_by;

    // 修改人
    private String modified_by;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime create_time;

    // 修改时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime update_time;

    // 创建时间范围
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private List<LocalDateTime> createTimeRange;

    // 修改时间范围
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private List<LocalDateTime> updateTimeRange;

    private Integer pageNo;   // 分页参数（非数据库字段）

    private Integer pageSize; // 分页参数（非数据库字段）

    private String sort;// 排序字段（非数据库字段）

    private String order;

    public static KnowledgebaseMapping convertKnowledgebaseMapping(KnowledgebaseMappingDTO dto) {
        KnowledgebaseMapping entity = new KnowledgebaseMapping();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setCreatedBy(dto.getCreated_by());
        entity.setModifiedBy(dto.getModified_by());
        entity.setCreateTime(dto.getCreate_time());
        entity.setUpdateTime(dto.getUpdate_time());
        return entity;
    }

    public static List<KnowledgebaseMappingTags> convertRelatedKnowledgeTags(KnowledgebaseMappingDTO dto) {
        List<KnowledgebaseMappingTags> mappingTagsList = new ArrayList<>();
        if (Objects.nonNull(dto) && Objects.nonNull(dto.getRelatedTags())){
            dto.getRelatedTags().forEach(tag -> {
                KnowledgebaseMappingTags mappingTags = new KnowledgebaseMappingTags()
                        .setTagId(tag)
                        .setKnowledgebaseMappingId(dto.getId())
                        .setCreateTime(LocalDateTime.now())
                        .setUpdateTime(dto.getUpdate_time());
                mappingTagsList.add(mappingTags);
            });
        }
        return mappingTagsList;
    }

}
