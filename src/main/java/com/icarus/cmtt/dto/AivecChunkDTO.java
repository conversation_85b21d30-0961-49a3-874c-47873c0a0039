package com.icarus.cmtt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 支持QA格式的文件切片表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class AivecChunkDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("切片ID")
    private String chunkId;

    @ApiModelProperty("所属文件ID（冗余存储，避免关联file表）")
    private String fileId;

    @ApiModelProperty("所属目录ID（冗余存储，避免关联directory表）")
    private String dirId;

    @ApiModelProperty("文件名（冗余存储）")
    private String fileName;

    @ApiModelProperty("文件minioKey（冗余存储）")
    private String fileMinioKey;

    @ApiModelProperty("目录名（冗余存储）")
    private String dirName;

    @ApiModelProperty("目录全路径（冗余存储）")
    private String dirPath;

    @ApiModelProperty("文件标签（冗余存储file.tags）")
    private String fileTags;


    @ApiModelProperty("切片内容（纯文本或问题部分）")
    private String chunkContent;

    @ApiModelProperty("显式问题（如：\"如何配置数据库？\"）")
    private String qaQuestion;

    @ApiModelProperty("问题对应的答案内容")
    private String qaAnswer;

    @ApiModelProperty("切片序号（用于重组原文）")
    @NotNull(message = "切片序号不能为空")
    private Integer chunkNumber;

    @ApiModelProperty("向量 embedding（用于RAG检索）")
    private String embeddingVector;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    //新增字段
    @ApiModelProperty("文件标签Id（默认带目录tags, JSON格式，如：[\"111\",\"2222\"]）")
    private String tagIds;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime publishTime;

    @ApiModelProperty("来源主体")
    private String sourceEntity;

    @ApiModelProperty("知识库体系")
    private String knowledgeBasePath;

    @ApiModelProperty("知识库ID")
    private String knowledgeBaseIds;

    @ApiModelProperty("应用名称")
    private String appName;

    private String taskId;



}
