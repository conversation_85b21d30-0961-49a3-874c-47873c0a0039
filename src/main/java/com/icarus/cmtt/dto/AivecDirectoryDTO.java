package com.icarus.cmtt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 目录树表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class AivecDirectoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String dirId;

    @ApiModelProperty("父目录ID（NULL表示根目录）")
    private String parentDirId;

    @ApiModelProperty("目录名称")

    private String dirName;

    @ApiModelProperty("目录全路径（如：/data/report/2023）")

    private String dirPath;

    @ApiModelProperty("目录标签（JSON格式，如：[\"项目资料\",\"公开文档\"]）")
    private String tags;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;


    //新增字段
    @ApiModelProperty("文件标签Id（默认带目录tags, JSON格式，如：[\"111\",\"2222\"]）")
    private String tagIds;

    @ApiModelProperty("应用名称")
    private String appName;

}
