package com.icarus.cmtt.dto;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.icarus.cmtt.entity.KnowledgeBaseSystem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class DifyDocumentDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("position")
    private int position;

    @JsonProperty("data_source_type")
    private String data_source_type;

    @JsonProperty("data_source_info")
    private DataSourceInfo data_source_info;

    @JsonProperty("data_source_detail_dict")
    private Map<String, UploadFile> data_source_detail_dict;

    @JsonProperty("dataset_process_rule_id")
    private String dataset_process_rule_id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("created_from")
    private String created_from;

    @JsonProperty("created_by")
    private String created_by;

    @JsonProperty("created_at")
    private long created_at;

    @JsonProperty("tokens")
    private int tokens;

    @JsonProperty("indexing_status")
    private String indexing_status;

    @JsonProperty("error")
    private String error;

    @JsonProperty("enabled")
    private boolean enabled;

    @JsonProperty("disabled_at")
    private Long disabled_at;

    @JsonProperty("disabled_by")
    private String disabled_by;

    @JsonProperty("archived")
    private boolean archived;

    @JsonProperty("display_status")
    private String display_status;

    @JsonProperty("word_count")
    private int word_count;

    @JsonProperty("hit_count")
    private int hit_count;

    @JsonProperty("doc_form")
    private String doc_form;

    @JsonProperty("doc_metadata")
    private Object doc_metadata;

    @Getter
    @Setter
    public static class DataSourceInfo {
        @JsonProperty("upload_file_id")
        private String upload_file_id;
    }

    @Getter
    @Setter
    public static class UploadFile {
        @JsonProperty("id")
        private String id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("size")
        private long size;

        @JsonProperty("extension")
        private String extension;

        @JsonProperty("mime_type")
        private String mime_type;

        @JsonProperty("created_by")
        private String created_by;

        @JsonProperty("created_at")
        private double created_at;

        // 可选：添加一个获取创建时间的Instant方法
        public Instant getCreatedAtInstant() {
            return Instant.ofEpochSecond((long) created_at);
        }
    }
    public static KnowledgeBaseSystem toEntity(DifyDocumentDTO dto) {
        KnowledgeBaseSystem entity = new KnowledgeBaseSystem();
        entity.setDifyId(dto.getId());
        entity.setName(dto.getName());
        entity.setProperty("file");
        entity.setOrder(String.valueOf(dto.getPosition()));
        entity.setCreatedBy(dto.getCreated_by());
        entity.setCreateTime(LocalDateTime.ofInstant(
                Instant.ofEpochMilli(dto.getCreated_at()),
                ZoneId.systemDefault()
        ));
        return entity;
    }
}
