package com.icarus.cmtt.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class AivecFileDTO implements Serializable {

    private String fileId;

    private String dirId;

    private String dirName;

    private String dirPath;

    private String fileName;

    private String fileMinioKey;

    private String fileType;

    private Long fileSize;

    private String tags;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String tagIds;

    private String knowledgeBasePath;

    private String knowledgeBaseIds;

    @ApiModelProperty("应用名称")
    private String appName;


}
