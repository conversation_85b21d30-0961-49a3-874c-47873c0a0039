package com.icarus.cmtt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icarus.cmtt.entity.CorpusScopeRule;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class KnowledgeBaseSystemDTO {

    private String id;

    private String name;

    private String property;

    private String parentId;

    private String description;

    private String associatedSystem;

    private String connectMode;

    private String ip;

    private String username;

    private String password;

    private String api;

    private String apiKey;

    private List<CorpusScopeRule> corpusScopeRules;

    private String sliceType;

    private Integer sliceCount;

    private String order;

    private String createdBy;

    private String modifiedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private Long corpusCount;

    private String difyId;

    private String fileDimension;

    private List<KnowledgeFileRules> knowledgeFileRules;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class KnowledgeFileRules {
        // 文件分组
        private String groupId;
        // 文件分组排序
        private String groupOrder;
        private String name;
        // 文件标签规则
        private List<CorpusScopeRule> rules;
    }

}
