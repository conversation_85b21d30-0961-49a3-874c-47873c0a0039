package com.icarus.cmtt.config;


import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * swagger地址:
 * dev -	http://localhost:9121/swagger-ui/index.html
 */
@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI selfOpenAPI() {
        return new OpenAPI().info(new Info()
                .title("AIP_CMTT API文档")
                .description("Spring Boot 3 应用接口文档")
                .version("v1.0.0"));
    }
}
