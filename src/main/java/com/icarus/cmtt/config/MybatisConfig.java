package com.icarus.cmtt.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.icarus.cmtt.typeHandler.ArrayTypeHandler;
import com.icarus.cmtt.typeHandler.FrequencyTypeHandler;
import com.icarus.cmtt.typeHandler.PgVectorListTypeHandler;
import com.icarus.cmtt.typeHandler.VectorTypeHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * MyBatis配置类
 * <p>
 * 用于配置MyBatis的全局设置，如注册自定义TypeHandler等
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Configuration
public class MybatisConfig implements InitializingBean {
    private final List<SqlSessionFactory> sqlSessionFactoryList;

    public MybatisConfig(List<SqlSessionFactory> sqlSessionFactoryList) {
        this.sqlSessionFactoryList = sqlSessionFactoryList;
    }

    @Override
    public void afterPropertiesSet() {
        // 添加分页拦截器
        MybatisPlusInterceptor paginationInterceptor = new MybatisPlusInterceptor();
        paginationInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            // 将拦截器注册到每个SqlSessionFactory中
            sqlSessionFactory.getConfiguration().addInterceptor(paginationInterceptor);
            // 注册自定义TypeHandler
            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
            registry.register(ArrayTypeHandler.class);
            registry.register(VectorTypeHandler.class);
            registry.register(PgVectorListTypeHandler.class);
            registry.register(FrequencyTypeHandler.class);
        }
    }
} 