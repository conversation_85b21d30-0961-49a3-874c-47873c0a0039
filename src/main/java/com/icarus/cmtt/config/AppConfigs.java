package com.icarus.cmtt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

@Data
@Configuration
@ConfigurationProperties(prefix = "app-configs")
public class AppConfigs implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    // aip 配置
    private AipConfig aip;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        AppConfigs.applicationContext = applicationContext;
    }

    // 获取当前实例的方法
    public static AppConfigs getInstance() {
        return Objects.requireNonNull(applicationContext.getBean(AppConfigs.class), "获取app-configs配置失败!");
    }

    // Static methods to access the fields
    public static AipConfig aip() {
        return Objects.requireNonNull(getInstance().getAip(), "获取大模型的默认配置失败!");
    }

    @Data
    public static class AipConfig {
        private String url;
        private String basePath;

        public String getSysReqUrl(){
            return url + basePath;
        }
    }

}