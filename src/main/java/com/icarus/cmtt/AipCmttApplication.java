package com.icarus.cmtt;


import com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication(scanBasePackages = {"com.icarus.common.minio", "com.icarus.cmtt"})
@MapperScan("com.icarus.cmtt.mapper")
public class AipCmttApplication {
    public static void main(String[] args) {
        SpringApplication.run(AipCmttApplication.class, args);
    }
}