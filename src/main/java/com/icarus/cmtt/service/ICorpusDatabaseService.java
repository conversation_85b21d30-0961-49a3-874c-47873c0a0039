package com.icarus.cmtt.service;

import com.icarus.cmtt.entity.CorpusDatabase;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ICorpusDatabaseService extends IService<CorpusDatabase> {

    List<String> getNodeId(String id);


    String getFullPath(String id);

    Map<String, String> getFullPathMap(List<String> ids);
}
