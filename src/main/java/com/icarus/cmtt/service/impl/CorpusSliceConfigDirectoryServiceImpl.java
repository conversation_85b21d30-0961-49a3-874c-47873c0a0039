package com.icarus.cmtt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.entity.CorpusSliceConfigDirectory;
import com.icarus.cmtt.mapper.CorpusSliceConfigDirectoryMapper;
import com.icarus.cmtt.service.ICorpusSliceConfigDirectoryService;
import org.springframework.stereotype.Service;

/**
 * IService 实现类 - CorpusSliceConfigDirectory
 */
@Service
public class CorpusSliceConfigDirectoryServiceImpl extends ServiceImpl<CorpusSliceConfigDirectoryMapper, CorpusSliceConfigDirectory> implements ICorpusSliceConfigDirectoryService {
}