package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.SampleLibrary;
import com.icarus.cmtt.mapper.SampleLibraryMapper;
import com.icarus.cmtt.service.ISampleLibraryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 样本库管理表，用于存储样本库和版本数据，采用自引用结构实现层级关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class SampleLibraryServiceImpl extends ServiceImpl<SampleLibraryMapper, SampleLibrary> implements ISampleLibraryService {

}
