package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.MessageAnalysisRecord;
import com.icarus.cmtt.mapper.MessageAnalysisRecordMapper;
import com.icarus.cmtt.service.IMessageAnalysisRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class MessageAnalysisRecordServiceImpl extends ServiceImpl<MessageAnalysisRecordMapper, MessageAnalysisRecord> implements IMessageAnalysisRecordService {

}
