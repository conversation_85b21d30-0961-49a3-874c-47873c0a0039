package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.CorpusSessionRecord;
import com.icarus.cmtt.mapper.CorpusSessionRecordMapper;
import com.icarus.cmtt.service.ICorpusSessionRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusSessionRecordServiceImpl extends ServiceImpl<CorpusSessionRecordMapper, CorpusSessionRecord> implements ICorpusSessionRecordService {

}
