package com.icarus.cmtt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.entity.CorpusSliceConfig;
import com.icarus.cmtt.mapper.CorpusSliceConfigMapper;
import com.icarus.cmtt.service.ICorpusSliceConfigService;
import org.springframework.stereotype.Service;

/**
 * IService 实现类 - CorpusSliceConfig
 */
@Service
public class CorpusSliceConfigServiceImpl extends ServiceImpl<CorpusSliceConfigMapper, CorpusSliceConfig> implements ICorpusSliceConfigService {
}
