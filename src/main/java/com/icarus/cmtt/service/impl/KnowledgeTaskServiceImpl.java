package com.icarus.cmtt.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.dto.DifyDocumentDTO;
import com.icarus.cmtt.dto.DifyFilesDTO;
import com.icarus.cmtt.entity.KnowledgeTask;
import com.icarus.cmtt.mapper.KnowledgeTaskMapper;
import com.icarus.cmtt.service.IKnowledgeTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * @description: 推送任务关联关系服务实现类
 */
@Service
@Slf4j
public class KnowledgeTaskServiceImpl  extends ServiceImpl<KnowledgeTaskMapper, KnowledgeTask>
        implements IKnowledgeTaskService {
    @Override
    public void saveRelation(String taskId,DifyFilesDTO difyFilesDTO) {
        ArrayList<KnowledgeTask> tasks = new ArrayList<>();
        for (DifyDocumentDTO difyDocumentDTO : difyFilesDTO.getDifyDocumentDTOS()) {
            KnowledgeTask knowledgeTask = new KnowledgeTask();
            knowledgeTask.setId(IdUtil.objectId());
            knowledgeTask.setKnowledgeId(difyFilesDTO.getId());
            knowledgeTask.setFileId(difyDocumentDTO.getId());
            knowledgeTask.setTaskId(taskId);
            tasks.add(knowledgeTask);
        }
        saveBatch( tasks);
        log.info("保存推送任务关联关系成功，taskId={}", taskId);
    }

}