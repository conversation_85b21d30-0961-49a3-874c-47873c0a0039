package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.RemoteApiConfig;
import com.icarus.cmtt.mapper.RemoteApiConfigMapper;
import com.icarus.cmtt.service.IRemoteApiConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 远程接口配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class RemoteApiConfigServiceImpl extends ServiceImpl<RemoteApiConfigMapper, RemoteApiConfig> implements IRemoteApiConfigService {

}
