package com.icarus.cmtt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.entity.CorpusSliceSegment;
import com.icarus.cmtt.mapper.CorpusSliceSegmentMapper;
import com.icarus.cmtt.service.ICorpusSliceSegmentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * IService 实现类 - CorpusSliceSegment
 */
@Service
public class CorpusSliceSegmentServiceImpl extends ServiceImpl<CorpusSliceSegmentMapper, CorpusSliceSegment>
        implements ICorpusSliceSegmentService {


    @Override
    public Integer getSumLengthByTags(List<String> tagIds) {
        return this.getBaseMapper().getSumLengthByTags(tagIds);
    }

    @Override
    public Integer getCountByTags(List<String> tagIds) {
        return this.getBaseMapper().getCountByTags(tagIds);
    }

    @Override
    public List<String> getIdsByTags(List<String> tagIds) {
        return this.getBaseMapper().getIdsByTags(tagIds);
    }
}