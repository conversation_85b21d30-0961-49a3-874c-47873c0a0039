package com.icarus.cmtt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.entity.CorpusSliceDocument;
import com.icarus.cmtt.mapper.CorpusSliceDocumentMapper;
import com.icarus.cmtt.service.ICorpusSliceDocumentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * IService 实现类 - CorpusSliceDocument
 */
@Service
public class CorpusSliceDocumentServiceImpl extends ServiceImpl<CorpusSliceDocumentMapper, CorpusSliceDocument>
        implements ICorpusSliceDocumentService {
    @Override
    public List<String> getFileIdsBySystemId(String id) {
        return this.baseMapper.getFileIdsBySystemId(id);
    }
}