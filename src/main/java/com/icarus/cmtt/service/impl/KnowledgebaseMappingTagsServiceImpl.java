package com.icarus.cmtt.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.dto.KnowledgebaseMappingDTO;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;
import com.icarus.cmtt.mapper.KnowledgebaseMappingTagsMapper;
import com.icarus.cmtt.service.IKnowledgebaseMappingTagsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KnowledgebaseMappingTagsServiceImpl extends ServiceImpl<KnowledgebaseMappingTagsMapper, KnowledgebaseMappingTags> implements IKnowledgebaseMappingTagsService {

    @Autowired
    private KnowledgebaseMappingTagsMapper kbmtMapper;

    @Override
    public Integer addKnowledgebaseMappingTags(KnowledgebaseMappingTags knowledgebaseMappingTags) {
        return kbmtMapper.insert(knowledgebaseMappingTags);
    }


    @Override
    public Integer deleteByBaseId(Integer id) {
        return kbmtMapper.deleteByBaseId(id);
    }

    @Override
    public List<KnowledgebaseMappingTags> findByBaseId(Integer id) {
        return kbmtMapper.findByBaseId(id);
    }

    @Override
    public IPage<KnowledgebaseMappingTags> queryKnowledgebaseMappingTags(KnowledgebaseMappingDTO queryDTO) {
        return null;
    }
}
