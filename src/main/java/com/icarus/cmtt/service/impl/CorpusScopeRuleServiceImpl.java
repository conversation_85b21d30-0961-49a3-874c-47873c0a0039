package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.CorpusScopeRule;
import com.icarus.cmtt.mapper.CorpusScopeRuleMapper;
import com.icarus.cmtt.service.ICorpusScopeRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service
public class CorpusScopeRuleServiceImpl extends ServiceImpl<CorpusScopeRuleMapper, CorpusScopeRule> implements ICorpusScopeRuleService {

    @Override
    public List<String> getSystemIdFromTags(String[] tags) {
        return this.baseMapper.getSystemIdFromTags(tags);
    }
}
