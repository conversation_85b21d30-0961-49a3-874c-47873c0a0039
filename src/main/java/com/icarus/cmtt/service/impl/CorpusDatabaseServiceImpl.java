package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.CorpusDatabase;
import com.icarus.cmtt.mapper.CorpusDatabaseMapper;
import com.icarus.cmtt.service.ICorpusDatabaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.micrometer.common.util.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusDatabaseServiceImpl extends ServiceImpl<CorpusDatabaseMapper, CorpusDatabase> implements ICorpusDatabaseService {

    @Override
    public List<String> getNodeId(String id) {
        return this.getBaseMapper().getNodeId(id);
    }

    @Override
    public String getFullPath(String id) {
        List<String> fullPath = getBaseMapper().getFullPath(id);
        if (fullPath != null && !fullPath.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fullPath.size(); i++) {
                sb.append(fullPath.get(i));
                if (i < fullPath.size() - 1) {
                    sb.append("/");
                }
            }
            return sb.toString();
        }
        return "";
    }

    @Override
    public Map<String, String> getFullPathMap(List<String> ids) {
        Map<String, String> map = new HashMap<>();
        for (String id : ids){
            if (StringUtils.isNotBlank(id) && !map.containsKey(id)){
                map.put(id, getFullPath(id));
            }
        }
        return map;
    }
}
