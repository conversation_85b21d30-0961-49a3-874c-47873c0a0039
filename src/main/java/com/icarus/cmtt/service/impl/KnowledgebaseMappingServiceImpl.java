package com.icarus.cmtt.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.dto.KnowledgebaseMappingDTO;
import com.icarus.cmtt.entity.CorpusTags;
import com.icarus.cmtt.entity.KnowledgebaseMapping;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;
import com.icarus.cmtt.mapper.KnowledgebaseMappingMapper;
import com.icarus.cmtt.service.ICorpusTagsService;
import com.icarus.cmtt.service.IKnowledgebaseMappingService;
import com.icarus.cmtt.service.IKnowledgebaseMappingTagsService;
import com.icarus.cmtt.vo.KnowledgebaseTagsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class KnowledgebaseMappingServiceImpl extends ServiceImpl<KnowledgebaseMappingMapper, KnowledgebaseMapping> implements IKnowledgebaseMappingService {

    @Autowired
    private KnowledgebaseMappingMapper knowledgebaseMappingMapper;

    @Autowired
    private IKnowledgebaseMappingTagsService kbmtService;

    @Autowired
    private ICorpusTagsService corpusTagsService;

    /**
     * 添加知识库映射标签
     * @param dto
     * @return
     */
    @Override
    public Integer addMappingTags(KnowledgebaseMappingDTO dto) {
        Integer id = ThreadLocalRandom.current().nextInt(1, Integer.MAX_VALUE);
        dto.setId(id);
        KnowledgebaseMapping kbm_create = KnowledgebaseMappingDTO.convertKnowledgebaseMapping(dto);
        List<KnowledgebaseMappingTags> mappingTagsList = KnowledgebaseMappingDTO.convertRelatedKnowledgeTags(dto);
        Integer i = knowledgebaseMappingMapper.insert(kbm_create);
        mappingTagsList.forEach(item -> kbmtService.save(item));
        return i;
    }

    /**
     * 添加知识库映射
     * @param corpusDatabase
     * @return
     */
    public Integer addKnowledgebaseMapping(KnowledgebaseMapping corpusDatabase){
        return knowledgebaseMappingMapper.insert(corpusDatabase);
    }

    /**
     * 删除知识库映射
     * @param id
     * @return
     */
    public Integer deleteKnowledgebaseMapping(Integer id){
        int i = this.baseMapper.deleteById(id);
        List<KnowledgebaseMappingTags> list = kbmtService.lambdaQuery().eq(KnowledgebaseMappingTags::getKnowledgebaseMappingId, id).list();
        list.forEach(item -> kbmtService.removeById(item.getId()));
        return i;
    }

    /**
     * 更新知识库映射
     * @param corpusDatabase
     * @return
     */
    public Integer updateKnowledgebaseMapping(KnowledgebaseMapping corpusDatabase){
        return knowledgebaseMappingMapper.updateById(corpusDatabase);
    }

    /**
     * 根据id查询知识库映射
     * @param id
     * @return
     */
    public KnowledgebaseMapping findKnowledgebaseMappingById(Integer id){
        return knowledgebaseMappingMapper.selectById(id);
    }

    /**
     * 查询所有语料库
     * @return
     */
    public List<KnowledgebaseMapping> findAllKnowledgebaseMappings(){
        return knowledgebaseMappingMapper.selectList(null);
    }

    /**
     * 根据条件查询标签列表
     *
     * @param queryDTO 查询条件
     * @return 标签列表
     */
    public IPage<KnowledgebaseTagsVo> queryKnowledgebaseMappings(KnowledgebaseMappingDTO queryDTO) {
        // 构建分页对象（使用DTO的分页参数）
        Page<KnowledgebaseTagsVo> page = new Page<>(
                queryDTO.getPageNo() != null ? queryDTO.getPageNo() : 1,
                queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10
        );

        // 执行Mapper查询
        IPage<KnowledgebaseTagsVo> knowledgebaseTagsVoIPage = knowledgebaseMappingMapper.selectKnowledgebaseTagsPage(page, queryDTO);

        Set<Integer> tagsIdsByRecord = getTagsIdsByRecord(knowledgebaseTagsVoIPage.getRecords());
        if (CollectionUtils.isEmpty(tagsIdsByRecord)){
            return knowledgebaseTagsVoIPage;
        }
        List<CorpusTags> corpusTags = corpusTagsService.listByIds(tagsIdsByRecord);

        knowledgebaseTagsVoIPage.getRecords().forEach(vo -> KnowledgebaseTagsVo.setTagName(vo, corpusTags));

        return knowledgebaseTagsVoIPage;
    }

    /**
     * 把取出tagid
     * @param records
     * @return
     */
    public Set<Integer> getTagsIdsByRecord(List<KnowledgebaseTagsVo> records){
        Set<Integer> tagIds = new HashSet<>();
        for(int i = 0; i < records.size(); i++) {
            KnowledgebaseTagsVo knowledgebaseTagsVo = records.get(i);
            String tags = knowledgebaseTagsVo.getTags();
            if (StrUtil.isBlank( tags))
                continue;
            List<String> tagList = Arrays.asList(tags.split(","));
            List<Integer> collect = tagList.stream()
                    .filter(s -> StrUtil.isNotBlank(s))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            tagIds.addAll(collect);
        }
        return tagIds;
    }

    @Override
    public Integer updateMappingTags(KnowledgebaseMappingDTO dtoUpdate) {
        KnowledgebaseMapping mapping = KnowledgebaseMappingDTO.convertKnowledgebaseMapping(dtoUpdate);
        int count = knowledgebaseMappingMapper.updateById(mapping);
        List<KnowledgebaseMappingTags> list = kbmtService.lambdaQuery().eq(KnowledgebaseMappingTags::getKnowledgebaseMappingId, dtoUpdate.getId()).list();
        list.forEach(item -> kbmtService.removeById(item.getId()));
        List<KnowledgebaseMappingTags> mappingTagsList = KnowledgebaseMappingDTO.convertRelatedKnowledgeTags(dtoUpdate);
        mappingTagsList.forEach(item -> kbmtService.save(item));
        return count;
    }

    @Override
    public KnowledgebaseTagsVo findByBaseId(Integer id) {
        KnowledgebaseMapping mapping = knowledgebaseMappingMapper.selectById(id);
        List<KnowledgebaseMappingTags> list = kbmtService.lambdaQuery().eq(KnowledgebaseMappingTags::getKnowledgebaseMappingId, id).list();

        if (!CollectionUtils.isEmpty(list)){
            List<Integer> tagIds = list.stream()
                    .map(KnowledgebaseMappingTags::getTagId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<CorpusTags> tagsList = corpusTagsService.listByIds(tagIds);
            return KnowledgebaseTagsVo.buildKnowledgebaseMappingVo(mapping, list, tagsList);
        }

        return KnowledgebaseTagsVo.buildKnowledgebaseMappingVo(mapping, list, null);
    }
}
