package com.icarus.cmtt.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.dto.CorpusTagsQueryDTO;
import com.icarus.cmtt.entity.CorpusTags;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;
import com.icarus.cmtt.mapper.CorpusTagsMapper;
import com.icarus.cmtt.service.ICorpusTagsService;
import com.icarus.cmtt.service.IKnowledgebaseMappingTagsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CorpusTagsServiceImpl  extends ServiceImpl<CorpusTagsMapper, CorpusTags> implements ICorpusTagsService {

    @Autowired
    private CorpusTagsMapper corpusTagsMapper;

    @Autowired
    private IKnowledgebaseMappingTagsService knowledgebaseMappingTagsService;

    @Override
    public Integer addTag(CorpusTags tag) {
        return corpusTagsMapper.insert(tag);
    }

    @Override
    public Integer deleteTagById(Integer id) {
        //TODO 删除标签和语料关联关系
        List<KnowledgebaseMappingTags> list = knowledgebaseMappingTagsService.lambdaQuery().eq(KnowledgebaseMappingTags::getTagId, id).list();
        if (CollectionUtils.isNotEmpty( list)){
            list.forEach(item -> knowledgebaseMappingTagsService.removeById(item.getId()));
        }
        return corpusTagsMapper.deleteById(id);
    }

    @Override
    public Integer updateTag(CorpusTags tag) {
        return corpusTagsMapper.updateById(tag);
    }

    @Override
    public CorpusTags findTag(Integer id) {
        return corpusTagsMapper.selectById(id);
    }

    @Override
    public List<CorpusTags> findTagList() {
        return list();
    }

    @Override
    public IPage<CorpusTags> queryTagListByCondition(CorpusTagsQueryDTO corpusTagsQueryDTO) {
        // 1. 创建分页对象
        Page<CorpusTags> page = new Page<>(corpusTagsQueryDTO.getPageNo(), corpusTagsQueryDTO.getPageSize());

        // 2. 构建查询条件
        QueryWrapper<CorpusTags> queryWrapper = new QueryWrapper<>();

        // 3. 添加动态查询条件（非空字段才作为查询条件）
        if (StringUtils.isNotBlank(corpusTagsQueryDTO.getTag())) {
            queryWrapper.eq("name", corpusTagsQueryDTO.getTag());
        }
        // 4. 执行分页查询
        return corpusTagsMapper.selectPage(page, queryWrapper);
    }

    /**
     * 获取关联的语料数量
     * @param tagId
     * @return
     */
    public Integer getCorpusCountByTagId(Integer tagId){
        return corpusTagsMapper.getCorpusCountByTagId(tagId);
    }

    /**
     * 获取关联的语料库数量
     * @param tagId
     * @return
     */
    public Integer getCorpusDataBaseCountByTagId(Integer tagId){
        return corpusTagsMapper.getCorpusDataBaseCountByTagId(tagId);
    }

    @Override
    public List<Double> convert(String str) {
        List<Double> vectorList = new ArrayList<>();
        if (StrUtil.isBlank(str)){
            return null;
        }

        // 移除字符串中的方括号
        String cleanedStr = str.replace("[", "").replace("]", "");

        // 按逗号分割字符串
        String[] elements = cleanedStr.split(",\\s*");

        // 将每个元素转换为 Double 并添加到列表中
        for (String element : elements) {
            vectorList.add(Double.parseDouble(element));
        }

        return vectorList;
    }

    @Override
    public List<String> getTagNameBySystemId(String id) {
        return this.getBaseMapper().getTagNameBySystemId(id);
    }

    @Override
    public String getFullPathName(String id) {
        return this.getBaseMapper().getFullPathName(Integer.parseInt(id));
    }

}
