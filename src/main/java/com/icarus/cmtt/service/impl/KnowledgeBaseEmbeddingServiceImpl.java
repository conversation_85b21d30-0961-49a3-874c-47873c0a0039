package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.KnowledgeBaseEmbedding;
import com.icarus.cmtt.mapper.KnowledgeBaseEmbeddingMapper;
import com.icarus.cmtt.service.IKnowledgeBaseEmbeddingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class KnowledgeBaseEmbeddingServiceImpl extends ServiceImpl<KnowledgeBaseEmbeddingMapper, KnowledgeBaseEmbedding> implements IKnowledgeBaseEmbeddingService {

}
