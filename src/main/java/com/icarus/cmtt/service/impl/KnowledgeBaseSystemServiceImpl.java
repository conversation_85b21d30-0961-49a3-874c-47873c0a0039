package com.icarus.cmtt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.icarus.cmtt.dto.DifyDocumentDTO;
import com.icarus.cmtt.dto.DifyFilesDTO;
import com.icarus.cmtt.dto.KnowledgeBaseSystemDTO;
import com.icarus.cmtt.dto.KnowledgebaseDTO;
import com.icarus.cmtt.entity.CorpusDetail;
import com.icarus.cmtt.entity.CorpusScopeRule;
import com.icarus.cmtt.entity.KnowledgeBaseSystem;
import com.icarus.cmtt.mapper.CorpusDetailMapper;
import com.icarus.cmtt.mapper.CorpusScopeRuleMapper;
import com.icarus.cmtt.mapper.KnowledgeBaseSystemMapper;
import com.icarus.cmtt.service.IKnowledgeBaseSystemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.cmtt.service.biz.VectorBizService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service
public class KnowledgeBaseSystemServiceImpl extends ServiceImpl<KnowledgeBaseSystemMapper, KnowledgeBaseSystem> implements IKnowledgeBaseSystemService {

    @Autowired
    private KnowledgeBaseSystemMapper knowledgeBaseSystemMapper;

    @Autowired
    private CorpusScopeRuleMapper corpusScopeRuleMapper;

    @Autowired
    private CorpusDetailMapper corpusDetailMapper;

    @Autowired
    @Lazy
    private VectorBizService vectorBizService;

    @Override
    @Transactional
    public Integer addKnowledgeBaseSystem(KnowledgeBaseSystemDTO dto) {
        KnowledgeBaseSystem knowledgeBaseSystem = new KnowledgeBaseSystem();
        BeanUtils.copyProperties(dto, knowledgeBaseSystem);

        String parentId = knowledgeBaseSystem.getParentId();
        String property = knowledgeBaseSystem.getProperty();
        Long count = knowledgeBaseSystemMapper.selectCount(new QueryWrapper<KnowledgeBaseSystem>()
                .eq(StringUtils.isNotBlank(parentId), "parent_id", parentId)
                .eq(StringUtils.isNotBlank(property), "property", property));
        knowledgeBaseSystem.setOrder(String.valueOf(count.intValue() + 1));

        Integer rs = knowledgeBaseSystemMapper.insert(knowledgeBaseSystem);
        if ((StringUtils.equals(dto.getProperty(),"knowledgebase") ||StringUtils.equals(dto.getProperty(),"file"))
                && !CollectionUtils.isEmpty(dto.getCorpusScopeRules())){

            List<CorpusScopeRule> corpusScopeRules = dto.getCorpusScopeRules();
            corpusScopeRules.forEach(corpusScopeRule -> {
                corpusScopeRule.setSystemId(knowledgeBaseSystem.getId());
                corpusScopeRule.setCreatedTime(LocalDateTime.now());
            });
            corpusScopeRules.forEach(item->corpusScopeRuleMapper.insert(item));

        }
        if("knowledgebase".equals(knowledgeBaseSystem.getProperty())){
            //关联切牌
            CompletableFuture.runAsync(()->{
                vectorBizService.combine(knowledgeBaseSystem.getId(),knowledgeBaseSystem.getFileDimension());
            });
        };

        return rs;
    }

    @Override
    @Transactional
    public Integer updateKnowledgeBaseSystem(KnowledgebaseDTO dto) {
        KnowledgeBaseSystem knowledgeBaseSystem = new KnowledgeBaseSystem();

        KnowledgeBaseSystemDTO knowledgeBaseSystemDTO = dto.getDto();
        BeanUtils.copyProperties(knowledgeBaseSystemDTO, knowledgeBaseSystem);
        if (StringUtils.isNotBlank(dto.getDifyId())){
            knowledgeBaseSystem.setDifyId(dto.getDifyId());
        }

        if (StringUtils.equals(knowledgeBaseSystemDTO.getProperty(),"knowledgebase") || StringUtils.equals(knowledgeBaseSystemDTO.getProperty(),"file")){
            //更新时先删除再插入
            corpusScopeRuleMapper.delete(new QueryWrapper<CorpusScopeRule>().eq("system_id",knowledgeBaseSystemDTO.getId()));

            List<CorpusScopeRule> rules = new ArrayList<>();

            // 安全地处理 corpusScopeRules
            Optional.ofNullable(knowledgeBaseSystemDTO.getCorpusScopeRules())
                    .ifPresent(corpusRules -> {
                        corpusRules.forEach(rule -> rule.setTagType("corpus"));
                        rules.addAll(corpusRules);
                    });

            // 安全地处理 systemFileRules
            Optional.ofNullable(knowledgeBaseSystemDTO.getKnowledgeFileRules())
                    .ifPresent(fileRules -> {
                        fileRules.forEach(systemFileRule -> {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            Optional.ofNullable(systemFileRule.getRules())
                                    .ifPresent(rulesList ->
                                            rulesList.forEach(rule -> {
                                                rule.setName(systemFileRule.getName());
                                                rule.setGroupId(uuid);
                                                rule.setGroupOrder(systemFileRule.getGroupOrder());
                                                rule.setTagType("chunk");
                                                rules.add(rule);
                                            })
                                    );
                        });
                    });


            if (!CollectionUtils.isEmpty(rules)){
                rules.forEach(corpusScopeRule -> {
                    corpusScopeRule.setSystemId(knowledgeBaseSystem.getId());
                    corpusScopeRule.setCreatedTime(LocalDateTime.now());
                });
                rules.forEach(item->corpusScopeRuleMapper.insert(item));
            }

            if("knowledgebase".equals(knowledgeBaseSystem.getProperty())){
//                关联切牌
               CompletableFuture.runAsync(()->{
                    vectorBizService.combine(knowledgeBaseSystem.getId(),knowledgeBaseSystem.getFileDimension());
                });
            };

        }

        int updated = knowledgeBaseSystemMapper.update(null, new LambdaUpdateWrapper<KnowledgeBaseSystem>()
                .eq(KnowledgeBaseSystem::getId, knowledgeBaseSystem.getId())

                // 更新 name 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getName()), KnowledgeBaseSystem::getName, knowledgeBaseSystem.getName())

                // 更新 property 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getProperty()), KnowledgeBaseSystem::getProperty, knowledgeBaseSystem.getProperty())

                // 更新 parentId 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getParentId()), KnowledgeBaseSystem::getParentId, knowledgeBaseSystem.getParentId())

                // 更新 associatedSystem 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getAssociatedSystem()), KnowledgeBaseSystem::getAssociatedSystem, knowledgeBaseSystem.getAssociatedSystem())

                // 更新 connectMode 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getConnectMode()), KnowledgeBaseSystem::getConnectMode, knowledgeBaseSystem.getConnectMode())

                // 更新 description 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getDescription()), KnowledgeBaseSystem::getDescription, knowledgeBaseSystem.getDescription())

                // 更新 ip 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getIp()), KnowledgeBaseSystem::getIp, knowledgeBaseSystem.getIp())

                // 更新 username 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getUsername()), KnowledgeBaseSystem::getUsername, knowledgeBaseSystem.getUsername())

                // 更新 password 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getPassword()), KnowledgeBaseSystem::getPassword, knowledgeBaseSystem.getPassword())

                // 更新 api 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getApi()), KnowledgeBaseSystem::getApi, knowledgeBaseSystem.getApi())

                // 更新 apiKey 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getApiKey()), KnowledgeBaseSystem::getApiKey, knowledgeBaseSystem.getApiKey())

                // 更新 sliceType 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getSliceType()), KnowledgeBaseSystem::getSliceType, knowledgeBaseSystem.getSliceType())

                // 更新 sliceCount 字段
                .set(Objects.nonNull(knowledgeBaseSystem.getSliceCount()), KnowledgeBaseSystem::getSliceCount, knowledgeBaseSystem.getSliceCount())

                // 更新 order 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getOrder()), KnowledgeBaseSystem::getOrder, knowledgeBaseSystem.getOrder())

                // 更新 createdBy 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getCreatedBy()), KnowledgeBaseSystem::getCreatedBy, knowledgeBaseSystem.getCreatedBy())

                // 更新 modifiedBy 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getModifiedBy()), KnowledgeBaseSystem::getModifiedBy, knowledgeBaseSystem.getModifiedBy())

                // 更新 createTime 字段
                .set(Objects.nonNull(knowledgeBaseSystem.getCreateTime()), KnowledgeBaseSystem::getCreateTime, knowledgeBaseSystem.getCreateTime())

                // 更新 updateTime 字段
                .set(Objects.nonNull(knowledgeBaseSystem.getUpdateTime()), KnowledgeBaseSystem::getUpdateTime, knowledgeBaseSystem.getUpdateTime())

                //更新 fileDimension 字段
                .set(StringUtils.isNotBlank(knowledgeBaseSystem.getFileDimension()), KnowledgeBaseSystem::getFileDimension, knowledgeBaseSystem.getFileDimension())

                // 更新 difyId 字段
                .set(
                        StringUtils.isNotBlank(knowledgeBaseSystem.getDifyId()), // 条件：非空且非空白
                        KnowledgeBaseSystem::getDifyId,
                        StringUtils.equals(knowledgeBaseSystem.getDifyId(), "setnull")
                                ? null
                                : knowledgeBaseSystem.getDifyId()
                )

        );


        return updated;
    }

    @Override
    @Transactional
    public Integer deleteKnowledgeBaseSystem(String id) {
        KnowledgeBaseSystem knowledgeBaseSystem = knowledgeBaseSystemMapper.selectById(id);
        if (Objects.isNull(knowledgeBaseSystem)){
            return 0;
        }
        String systemId = knowledgeBaseSystem.getId();
        corpusScopeRuleMapper.delete(new QueryWrapper<CorpusScopeRule>().eq("system_id", systemId));
        return knowledgeBaseSystemMapper.deleteById(id);
    }

    @Override
    public KnowledgeBaseSystemDTO getKnowledgeBaseSystem(String id) {
        if (StringUtils.isBlank(id)){
            return null;
        }
        KnowledgeBaseSystemDTO dto = new KnowledgeBaseSystemDTO();

        KnowledgeBaseSystem knowledgeBaseSystem = knowledgeBaseSystemMapper.selectById(id);
        if (Objects.isNull(knowledgeBaseSystem)){
            return null;
        }
        BeanUtils.copyProperties(knowledgeBaseSystem, dto);

        String property = knowledgeBaseSystem.getProperty();
        //获取知识库系统直接返回主表
        if (StringUtils.equals(property, "management")){
            return dto;
        }

        List<CorpusScopeRule> corpusScopeRules = corpusScopeRuleMapper
                .selectList(new LambdaQueryWrapper<CorpusScopeRule>()
                        .eq(CorpusScopeRule::getSystemId, knowledgeBaseSystem.getId())
                        .orderByAsc(CorpusScopeRule::getOrder));
        //根据type不同放到语料范围还是切片范围

        List<CorpusScopeRule> scopeRuleList = corpusScopeRules.stream()
                .filter(corpusScopeRule ->StringUtils.equals(corpusScopeRule.getTagType(), "corpus"))
                .sorted(Comparator.comparing(
                        rule -> Integer.parseInt(rule.getOrder()),
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
        dto.setCorpusScopeRules(scopeRuleList);

        List<KnowledgeBaseSystemDTO.KnowledgeFileRules> knowledgeFileRules = this.getKnowledgeFileRules(corpusScopeRules);
        dto.setKnowledgeFileRules(knowledgeFileRules);


        //知识库还要查语料数量
        if (StringUtils.equals(property, "knowledgebase")){
            Long corpusCount = getCorpusCountByTags(scopeRuleList);
            dto.setCorpusCount(corpusCount);

        }
        return dto;
    }

    /**
     * 获取知识库系统文件规则
     * @param corpusScopeRules
     * @return
     */
    private List<KnowledgeBaseSystemDTO.KnowledgeFileRules> getKnowledgeFileRules(List<CorpusScopeRule> corpusScopeRules) {
        // 添加空值检查
        if (CollectionUtil.isEmpty(corpusScopeRules)) {
            return Collections.emptyList();
        }
        // 按 groupId 分组并转换为 SystemFileRule 结构
        Map<String, List<CorpusScopeRule>> groupedChunks = corpusScopeRules.stream()
                .filter(rule -> StringUtils.equals(rule.getTagType(), "chunk"))
                .collect(Collectors.groupingBy(CorpusScopeRule::getGroupId));

        if (CollectionUtil.isEmpty(groupedChunks)){
            return Collections.emptyList();
        }
        // 转换为 SystemFileRule 列表并按 groupOrder 排序
        List<KnowledgeBaseSystemDTO.KnowledgeFileRules> systemFileRules = groupedChunks.entrySet().stream()
                .map(entry -> {
                    String groupId = entry.getKey();
                    List<CorpusScopeRule> rules = entry.getValue();

                    // 获取 groupOrder（从组内任一规则获取，因为同一组内应该相同）
                    String groupOrder = rules.isEmpty() ? "0" : rules.get(0).getGroupOrder();

                    // 对 groupOrder 进行验证，确保是有效数字
                    if (groupOrder == null || !groupOrder.matches("\\d+")) {
                        groupOrder = "0";
                    }
                    // 对组内规则按 order 排序
                    List<CorpusScopeRule> sortedRules = rules.stream()
                            .sorted(Comparator.comparing(
                                    rule -> Integer.parseInt(rule.getOrder()),
                                    Comparator.nullsLast(Comparator.naturalOrder())
                            ))
                            .collect(Collectors.toList());

                    // 创建 SystemFileRule 对象
                    KnowledgeBaseSystemDTO.KnowledgeFileRules systemFileRule = new KnowledgeBaseSystemDTO.KnowledgeFileRules();
                    systemFileRule.setGroupId(groupId);
                    systemFileRule.setGroupOrder(groupOrder);
                    systemFileRule.setRules(sortedRules);
                    systemFileRule.setName(rules.get(0).getName());

                    return systemFileRule;
                })
                .sorted(Comparator.comparing(
                        rule -> Integer.parseInt(rule.getGroupOrder()),
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
        return systemFileRules;
    }
    /**
     * 获取语料数量
     * @param conditions
     * @return
     */
    @Override
    public Long getCorpusCountByTags(List<CorpusScopeRule> conditions){
        if (conditions == null || conditions.isEmpty()) {
            return 0L;
        }

        QueryWrapper<CorpusDetail> wrapper = new QueryWrapper<>();
        StringBuilder sqlBuilder = new StringBuilder();

        for (int i = 0; i < conditions.size(); i++) {
            CorpusScopeRule condition = conditions.get(i);
            String value = condition.getValue();
            String operator = condition.getOperator(); // CONTAIN, EQUAL, NOTEQUAL, NOTCONTAIN
            String logicWithPrev = condition.getLogicWithPrev(); // AND, OR

            if (value == null || value.trim().isEmpty()) {
                continue;
            }

            String[] tags = value.split(",");
            if (tags.length == 0) {
                continue;
            }

            if (i > 0 && logicWithPrev != null) {
                sqlBuilder.append(" ").append(logicWithPrev).append(" ");
            }



            sqlBuilder.append("(").append(buildConditionExpression(operator, tags)).append(")");
        }

        if (sqlBuilder.length() > 0) {
            wrapper.apply("(" + sqlBuilder.toString() + ")");
            return corpusDetailMapper.selectCount(wrapper);
        }
        return 0L;
    }

    @Override
    public Object saveFiles(DifyFilesDTO difyFilesDTO) {
        List<KnowledgeBaseSystem> rs = new ArrayList<>();

        String difyId = difyFilesDTO.getId();
        List<DifyDocumentDTO> difyDocumentDTOS = difyFilesDTO.getDifyDocumentDTOS();

        KnowledgeBaseSystem parent = this.lambdaQuery()
                .eq(KnowledgeBaseSystem::getDifyId, difyId).one();

        if (parent == null) {
            throw new IllegalArgumentException("Parent not found by difyId: " + difyId);
            // 或者根据业务需求返回错误信息、空对象等
        }

        List<KnowledgeBaseSystem> list = this.lambdaQuery()
                .eq(KnowledgeBaseSystem::getParentId, parent.getId())
                .eq(KnowledgeBaseSystem::getProperty, "file")
                .orderByAsc(KnowledgeBaseSystem::getOrder).list();

        if (list == null) {
            list = Collections.emptyList();
        }

        Set<String> existingIds = list.stream().map(KnowledgeBaseSystem::getDifyId).collect(Collectors.toSet());
        // 过滤掉 list 中已有的 id
        List<DifyDocumentDTO> collect = difyDocumentDTOS.stream().filter(e -> !existingIds.contains(e.getId()))
                .collect(Collectors.toList());

        List<KnowledgeBaseSystem> newFiles = collect.stream()
                .map(difyDocumentDTO -> {
                    KnowledgeBaseSystem entity = DifyDocumentDTO.toEntity(difyDocumentDTO);
                    entity.setParentId(parent.getId()); // 确保 difyId 已赋值
                    return entity;
                })
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(newFiles)){
            return list;
        }

        newFiles.forEach(entity -> knowledgeBaseSystemMapper.insert(entity));

        rs.addAll(list);
        rs.addAll(newFiles);
        rs = rs.stream()
                .sorted(Comparator.comparing(
                        KnowledgeBaseSystem::getOrder,
                        Comparator.nullsLast(Comparator.comparingInt(Integer::parseInt))
                ))
                .collect(Collectors.toList());

        return rs;
    }

    @Override
    public String getFullPath(String id) {
        List<String> fullPath = getBaseMapper().getFullPath(id);
        if (fullPath != null && !fullPath.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fullPath.size(); i++) {
                sb.append(fullPath.get(i));
                if (i < fullPath.size() - 1) {
                    sb.append("/");
                }
            }
            return sb.toString();
        }
        return "";
    }

    @Override
    public Object data(JSONObject jsonObject) {
        return null;
    }

    /**
     * 构建条件表达式，等于/不等于时忽略顺序
     * // CONTAIN, EQUAL, NOTEQUAL, NOTCONTAIN
     */
    private String buildConditionExpression(String operator, String[] tags) {
        String arrayExpr = buildArrayExpression(tags);
        switch (operator == null ? "CONTAIN" : operator.toUpperCase()) {
            case "CONTAIN":
                // 包含所有标签
                return "tags @> " + arrayExpr;
            case "EQUAL":
                // 完全相等（忽略顺序）
                return "tags @> " + arrayExpr + " AND tags <@ " + arrayExpr;
            case "NOTEQUAL":
                // 不等于（忽略顺序）
                return "NOT (tags @> " + arrayExpr + " AND tags <@ " + arrayExpr + ")";
            case "NOTCONTAIN":
                // 不包含任何一个标签
                return "NOT (tags && " + arrayExpr + ")";
            default:
                return "tags @> " + arrayExpr;
        }
    }

    /**
     * 构建 PostgreSQL 数组表达式
     */
    private String buildArrayExpression(String[] tags) {
        StringBuilder arrayBuilder = new StringBuilder("ARRAY[");
        for (int j = 0; j < tags.length; j++) {
            if (j > 0) {
                arrayBuilder.append(",");
            }
            arrayBuilder.append("'").append(tags[j].trim()).append("'");
        }
        arrayBuilder.append("]::text[]");
        return arrayBuilder.toString();
    }

    @Override
    public String getKnowledgeBaseSystemTree() {
        return null;
    }

    public Integer renameKnowledgeBaseSystem(KnowledgeBaseSystemDTO dto){
        KnowledgeBaseSystem knowledgeBaseSystem = new KnowledgeBaseSystem();
        knowledgeBaseSystem.setId(dto.getId());
        knowledgeBaseSystem.setName(dto.getName());
        knowledgeBaseSystem.setUpdateTime(dto.getUpdateTime());
        knowledgeBaseSystem.setModifiedBy(dto.getModifiedBy());
        return knowledgeBaseSystemMapper.updateById(knowledgeBaseSystem);
    }

    @Override
    public List<String> getNodes(String id) {
        return this.getBaseMapper().getNodes(id);
    }

}
