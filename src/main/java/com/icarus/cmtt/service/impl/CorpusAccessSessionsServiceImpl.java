package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.CorpusAccessSessions;
import com.icarus.cmtt.mapper.CorpusAccessSessionsMapper;
import com.icarus.cmtt.service.ICorpusAccessSessionsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusAccessSessionsServiceImpl extends ServiceImpl<CorpusAccessSessionsMapper, CorpusAccessSessions> implements ICorpusAccessSessionsService {

}
