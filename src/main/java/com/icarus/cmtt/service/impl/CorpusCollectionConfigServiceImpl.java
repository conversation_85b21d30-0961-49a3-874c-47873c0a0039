package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.CorpusCollectionConfig;
import com.icarus.cmtt.mapper.CorpusCollectionConfigMapper;
import com.icarus.cmtt.service.ICorpusCollectionConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusCollectionConfigServiceImpl extends ServiceImpl<CorpusCollectionConfigMapper, CorpusCollectionConfig> implements ICorpusCollectionConfigService {

    @Resource
    private CorpusCollectionConfigMapper corpusCollectionConfigMapper;

    @Override
    public List<String> selectIdsByFrequency(String frequency, String startTime, String endTime) {
        return corpusCollectionConfigMapper.selectIdsByFrequency(frequency, startTime, endTime);
    }


}
