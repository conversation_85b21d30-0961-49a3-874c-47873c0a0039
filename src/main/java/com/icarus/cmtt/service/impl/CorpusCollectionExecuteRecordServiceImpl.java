package com.icarus.cmtt.service.impl;

import com.icarus.cmtt.entity.CorpusCollectionExecuteRecord;
import com.icarus.cmtt.mapper.CorpusCollectionExecuteRecordMapper;
import com.icarus.cmtt.service.ICorpusCollectionExecuteRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusCollectionExecuteRecordServiceImpl extends ServiceImpl<CorpusCollectionExecuteRecordMapper, CorpusCollectionExecuteRecord> implements ICorpusCollectionExecuteRecordService {

}
