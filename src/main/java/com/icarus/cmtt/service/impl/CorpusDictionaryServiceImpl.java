package com.icarus.cmtt.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.cmtt.entity.CorpusDictionary;
import com.icarus.cmtt.mapper.CorpusDictionaryMapper;
import com.icarus.cmtt.service.ICorpusDictionaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.micrometer.common.util.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusDictionaryServiceImpl extends ServiceImpl<CorpusDictionaryMapper, CorpusDictionary> implements ICorpusDictionaryService {
    @Override
    public String getSourceEntityPathById(String id) {
        StringBuilder sb = new StringBuilder();
        BaseMapper<CorpusDictionary> mapper = getBaseMapper();
        CorpusDictionary entity = mapper.selectById(id);
        sb.append(entity.getDictValue());
        while (Strings.isNotBlank(entity.getParentId())) {
            entity = mapper.selectById(entity.getParentId());
            sb.insert(0, entity.getDictValue() + "/");
        }
        return sb.toString();
    }

    @Override
    public Map<String, String> getSourceEntityPathMap(List<String> ids) {
        Map<String, String> map = new HashMap<>();
        for (String id : ids){
            if (StringUtils.isNotBlank(id) && !map.containsKey(id)){
                map.put(id, getSourceEntityPathById(id));
            }
        }
        return map;
    }
}
