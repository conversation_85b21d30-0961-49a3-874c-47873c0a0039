package com.icarus.cmtt.service;

import com.icarus.cmtt.entity.CorpusDictionary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ICorpusDictionaryService extends IService<CorpusDictionary> {

    String getSourceEntityPathById(String id);

    Map<String, String> getSourceEntityPathMap(List<String> ids);
}
