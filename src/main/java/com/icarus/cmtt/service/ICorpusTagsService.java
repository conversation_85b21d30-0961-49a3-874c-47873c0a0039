package com.icarus.cmtt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.cmtt.dto.CorpusTagsQueryDTO;
import com.icarus.cmtt.entity.CorpusTags;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ICorpusTagsService extends IService<CorpusTags> {

    //增加标签
    Integer addTag(CorpusTags tag);

    //删除标签
    Integer deleteTagById(Integer id);

    //修改标签
    Integer updateTag(CorpusTags tag);

    //查询标签
    CorpusTags findTag(Integer id);

    //查询标签列表
    List<CorpusTags> findTagList();

    //查询标签列表
    IPage<CorpusTags> queryTagListByCondition(CorpusTagsQueryDTO corpusTagsQueryDTO);

    //查询关联语料数量
    Integer getCorpusCountByTagId(Integer tagId);

    //查询关联语料库数量
    Integer getCorpusDataBaseCountByTagId(Integer tagId);

    List<Double> convert(String str);

    List<String> getTagNameBySystemId(String id);

    String getFullPathName(String id);
}
