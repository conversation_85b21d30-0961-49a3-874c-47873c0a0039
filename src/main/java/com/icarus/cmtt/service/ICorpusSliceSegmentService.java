package com.icarus.cmtt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.cmtt.entity.CorpusSliceSegment;

import java.util.List;

/**
 * IService 接口 - CorpusSliceSegment
 */
public interface ICorpusSliceSegmentService extends IService<CorpusSliceSegment> {

    Integer getSumLengthByTags(List<String> tagIds);

    Integer getCountByTags(List<String> tagIds);

    List<String> getIdsByTags(List<String> tagIds);
}