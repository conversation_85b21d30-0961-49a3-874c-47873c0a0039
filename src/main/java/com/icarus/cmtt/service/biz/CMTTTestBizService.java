package com.icarus.cmtt.service.biz;

import com.icarus.cmtt.entity.CorpusDetail;
import com.icarus.cmtt.service.ICorpusDetailService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业务服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Slf4j
@Service
public class CMTTTestBizService {

    @Resource
    private ICorpusDetailService corpusDetailService;


    public void testCmttDataSource() {
        List<CorpusDetail> list = corpusDetailService.list();
        log.info("list size:{}", list.size());
    }

}
