package com.icarus.cmtt.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.beust.ah.A;
import com.icarus.cmtt.dto.AivecChunkDTO;
import com.icarus.cmtt.dto.AivecDirectoryDTO;
import com.icarus.cmtt.dto.AivecFileDTO;
import com.icarus.cmtt.entity.*;
import com.icarus.cmtt.enums.AipApiEnum;
import com.icarus.cmtt.enums.OperationType;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.util.AipApiClientUtil;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 向量相关接口实现类
 */
@Service
@Slf4j
public class VectorBizService {

    private static  final  String DIR = "DIR";
    private static  final  String FILE = "FILE";
    private static  final  String CHUNK = "CHUNK";


    @Value("${spring.application.name}")
    private String appName;

    @Resource
    private ICorpusDatabaseService databaseService;

    @Resource
    private ICorpusFileService fileService;

    @Resource
    private ICorpusDetailService corpusDetailService;

    @Resource
    private ICorpusScopeRuleService corpusScopeRuleService;

    @Resource
    private IKnowledgeBaseSystemService knowledgeBaseSystemService;

    @Resource
    private ICorpusTagsService corpusTagsService;

    @Resource
    private ICorpusCollectionConfigService collectionConfigService;

    @Resource
    private ICorpusSliceTaskService corpusSliceTaskService;

    @Resource
    private ICorpusSliceDocumentService corpusSliceDocumentService;

    @Resource
    private ICorpusSliceSegmentService corpusSliceSegmentService;

    /**
     * 查询目录
     * @param infoId 目录id
     * @return 目录数据
     */
    public JSONObject getInfo(String type,String infoId) {
        String result = AipApiClientUtil.callApi(AipApiEnum.vector.getPath(),
                makeAipBody(OperationType.GET,type,infoId));
        return parseAipResult(result);
    }


    /**
     * 递归创建目录
     * @param directoryId 目录id
     */
    public void createDirectory(String directoryId) {
        CorpusDatabase corpusDatabase = databaseService.getById(directoryId);
        if (corpusDatabase==null){
            log.error("语料目录不存在{}", directoryId);
            throw new RuntimeException("语料目录不存在");
        }
        //当前目录是否存在存在
        JSONObject directory = getInfo(DIR,directoryId);
        if (directory!=null){
            log.info("语料目录已存在{}", directoryId);
            return;
        }
        //创建目录对象
        AivecDirectoryDTO aivecDirectoryDTO = transferDirectory(corpusDatabase);
        log.info("创建向量库目录: {}", corpusDatabase);
        String result = AipApiClientUtil.callApi(AipApiEnum.vector.getPath(), makeAipBody(OperationType.CREATE, DIR, aivecDirectoryDTO));
        JSONObject resultData = parseAipResult(result);
        if (resultData==null){
            log.error("创建向量目录失败{}", aivecDirectoryDTO);
            throw new RuntimeException("创建语料目录失败");
        }
        //递归向上创建目录
        if (corpusDatabase.getParentId()!=null){
            createDirectory(corpusDatabase.getParentId());
        }
    }


    /**
     * 创建向量文件
     * @param fileId 文件id
     */
    public void createFile(String fileId){
        CorpusFile corpusFile = fileService.getById(fileId);
        if (corpusFile==null){
            log.error("语料库文件不存在,fileId:{}",fileId);
            throw new RuntimeException("语料库文件不存在");
        }
        AivecFileDTO aivecFileDTO = transferFile(corpusFile);
        log.info("创建向量库文件: {}", aivecFileDTO);
        // 检查目录是否存在
        createDirectory(aivecFileDTO.getDirId());
        // 创建向量库文件
        String result = AipApiClientUtil.callApi(AipApiEnum.vector.getPath(), makeAipBody(OperationType.CREATE, FILE, aivecFileDTO));
        JSONObject aipResult = parseAipResult(result);
        if (aipResult==null){
            log.error("创建向量库文件失败: {}", aivecFileDTO);
        }
    }

    public void createChunk(CorpusSliceSegment segment){
        AivecChunkDTO aivecChunkDTO = transferChunk(segment);
        log.info("创建向量库切片: {}", aivecChunkDTO);
        String result = AipApiClientUtil.callApi(AipApiEnum.vector.getPath(), makeAipBody(OperationType.CREATE, CHUNK, aivecChunkDTO));
        JSONObject aipResult = parseAipResult(result);
        if (aipResult==null){
            log.error("创建向量库切片失败: {}", aivecChunkDTO);
        }
    }

    private AivecChunkDTO transferChunk(CorpusSliceSegment segment) {
        CorpusSliceTask sliceTask = corpusSliceTaskService.getById(segment.getSliceTaskId());
        AivecFileDTO aivecFileDTO = null;
        AivecChunkDTO aivecChunkDTO = new AivecChunkDTO();
        CorpusDetail corpusDetail = null;
        CorpusCollectionConfig collectionConfig = null;
        if (0==sliceTask.getMode()) {
            //先判断有没有文件信息
            JSONObject info = getInfo(FILE, segment.getFileId());
            if (info == null) {
                log.info("创建向量文件信息,文件id：{}", segment.getFileId());
                createFile(segment.getFileId());
            }
            //获取已入库的文件信息
            try {
                aivecFileDTO = JSONUtil.toBean(info, AivecFileDTO.class);
            } catch (Exception e) {
                log.error("转化向量文件失败");
                throw new RuntimeException(e);
            }
            //获取语料 配置信息 createFile中校验是否为null
             CorpusFile corpusFile = fileService.lambdaQuery().select(CorpusFile::getCorpusId).eq(CorpusFile::getId, segment.getFileId()).one();
            if (corpusFile== null){
                log.error("语料库文件不存在,语料id:{}", corpusFile.getId());
                throw new RuntimeException("语料库配置不存在");
            }
             corpusDetail = corpusDetailService.lambdaQuery().select(CorpusDetail::getCorpusCollectionConfigId, CorpusDetail::getCreateTime).eq(CorpusDetail::getId, corpusFile.getCorpusId()).one();
            if (corpusDetail== null){
                log.error("语料不存在,语料id:{}", corpusDetail.getId());
                throw new RuntimeException("语料不存在");
            }
             collectionConfig = collectionConfigService.lambdaQuery().select(
                    CorpusCollectionConfig::getSourceEntity).eq(CorpusCollectionConfig::getId, corpusDetail.getCorpusCollectionConfigId()).one();
            if (collectionConfig == null) {
                log.error("语料库配置不存在,语料id:{}", corpusDetail.getId());
                throw new RuntimeException("语料库配置不存在");
            }
        }
        aivecChunkDTO.setChunkId(segment.getId());
        aivecChunkDTO.setFileId(segment.getFileId());
        aivecChunkDTO.setTaskId(segment.getSliceTaskId().toString());
        if (aivecFileDTO!=null) {
            aivecChunkDTO.setDirId(aivecFileDTO.getDirId());
            aivecChunkDTO.setDirName(aivecFileDTO.getDirName());
            aivecChunkDTO.setFileName(aivecFileDTO.getFileName());
            aivecChunkDTO.setFileMinioKey(aivecFileDTO.getFileMinioKey());
            aivecChunkDTO.setDirPath(aivecFileDTO.getDirPath());
            aivecChunkDTO.setFileTags(aivecFileDTO.getTags());
            aivecChunkDTO.setTagIds(aivecFileDTO.getTagIds());
            aivecChunkDTO.setKnowledgeBasePath(aivecFileDTO.getKnowledgeBasePath());
            aivecChunkDTO.setKnowledgeBaseIds(aivecFileDTO.getKnowledgeBaseIds());
        }
        aivecChunkDTO.setAppName(appName);
        aivecChunkDTO.setChunkNumber(segment.getIndex());
        aivecChunkDTO.setChunkContent(segment.getContent());
        if (collectionConfig!=null) {
            aivecChunkDTO.setSourceEntity(collectionConfig.getSourceEntity());
        }
        if (corpusDetail!=null) {
            aivecChunkDTO.setPublishTime(corpusDetail.getCreateTime());
        }
        return aivecChunkDTO;

    }

    /**
     * 构建aip请求体
     * @param operationType 操作类型
     * @param operationObject 操作对象
     * @param object 请求体
     * @return 返回结果
     */
    private JSONObject makeAipBody(OperationType operationType,String operationObject,Object object){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationType", operationType);
        jsonObject.put("operationObject", operationObject);
        jsonObject.put("bodyJson", JSONUtil.toJsonStr(object));
        return jsonObject;
    }

    /**
     * 转换目录
     * @param corpusDatabase 语料目录
     * @return AivecDirectoryDTO 向量库目录
     */
    private AivecDirectoryDTO transferDirectory(CorpusDatabase corpusDatabase) {
        AivecDirectoryDTO aivecDirectoryDTO = new AivecDirectoryDTO();
        aivecDirectoryDTO.setDirId(corpusDatabase.getId());
        aivecDirectoryDTO.setParentDirId(corpusDatabase.getParentId());
        aivecDirectoryDTO.setDirName(corpusDatabase.getName());
        aivecDirectoryDTO.setDirPath(databaseService.getFullPath(corpusDatabase.getId()));
        aivecDirectoryDTO.setAppName(appName);
        return  aivecDirectoryDTO;
    }

    private AivecFileDTO transferFile(CorpusFile corpusFile){
        //获取对应的目录
        CorpusDetail corpusDetail = corpusDetailService.<CorpusDetail>lambdaQuery()
                .select(CorpusDetail::getCorpusDatabaseId,CorpusDetail::getTags).eq(CorpusDetail::getId, corpusFile.getCorpusId()).one();
        if (corpusDetail==null){
            log.error("文件对应语料不存在");
            throw new RuntimeException("文件对应语料不存在");
        }
        CorpusDatabase corpusDatabase = databaseService.<CorpusDatabase>lambdaQuery().select(CorpusDatabase::getName)
                .eq(CorpusDatabase::getId, corpusDetail.getCorpusDatabaseId()).one();
        if (corpusDatabase==null){
            log.error("语料对应的目录不存在");
            throw new RuntimeException("语料对应的目录不存在");
        }
        //获取语料对应的知识库目录
        List<String> knowledgeBaseIds = corpusScopeRuleService.getSystemIdFromTags(corpusDetail.getTags());
        List<String>  knowledgeBasePath = new ArrayList<>();
        for (String knowledgeBaseId : knowledgeBaseIds) {
            String fullPath = knowledgeBaseSystemService.getFullPath(knowledgeBaseId);
            knowledgeBasePath.add(fullPath);
        }
        List<Integer> integerList = Arrays.stream(corpusDetail.getTags())
                .map(Integer::parseInt)  // 将每个字符串转为 Integer
                .collect(Collectors.toList());
        List<String> tags;
        if (integerList.isEmpty()){
            tags = new ArrayList<>();
        }else {
            //获取标签名称
            tags = corpusTagsService.lambdaQuery()
                    .select(CorpusTags::getName).in(CorpusTags::getId, integerList)
                    .list().stream().map(CorpusTags::getName).collect(Collectors.toList());

        }
        AivecFileDTO aivecFileDTO = new AivecFileDTO();
        aivecFileDTO.setFileId(corpusFile.getId());
        aivecFileDTO.setDirId(corpusDetail.getCorpusDatabaseId());
        aivecFileDTO.setDirName(corpusDatabase.getName());
        aivecFileDTO.setFileName(corpusFile.getName());
        aivecFileDTO.setDirPath(databaseService.getFullPath(corpusDetail.getCorpusDatabaseId()));
        aivecFileDTO.setFileMinioKey(corpusFile.getMinioFileKey());
        aivecFileDTO.setFileMinioKey(corpusFile.getMinioFileKey());
        aivecFileDTO.setFileType(corpusFile.getType());
        aivecFileDTO.setFileSize(corpusFile.getFileSize().longValue());
        aivecFileDTO.setKnowledgeBaseIds(JSONUtil.toJsonStr(knowledgeBaseIds));
        aivecFileDTO.setKnowledgeBasePath(JSONUtil.toJsonStr(knowledgeBasePath));
        aivecFileDTO.setTagIds(JSONUtil.toJsonStr(corpusDetail.getTags()));
        aivecFileDTO.setTags(JSONUtil.toJsonStr(tags));
        aivecFileDTO.setAppName(appName);

        return aivecFileDTO;
    }

    public JSONObject parseAipResult(String result){
        try {
            JSONObject root = new JSONObject(result);

            // 检查第一层data
            if (!root.containsKey("data")) {
                return null;
            }

            JSONObject firstLevelData = root.getJSONObject("data");

            // 检查result是否存在且是字符串
            if (!firstLevelData.containsKey("result")) {
                return null;
            }

            // 解析result字符串为JSON
            JSONObject resultJson = new JSONObject(firstLevelData.getStr("result"));

            // 检查result中的data
            if (resultJson.containsKey("data")) {
                return resultJson.getJSONObject("data");
            }

            return null;
        } catch (Exception e) {
            // 记录错误或抛出异常（根据你的需求）
            log.error("参数：{},JSON解析错误: {}",result, e.getMessage());
            return null;
        }
    }

    /**
     * 关联知识库和切片
     * @param id
     */
    public void combine(String id,String fileDimension) {
        CombineDTO combineDTO = new CombineDTO();
        combineDTO.setSystemId(id);
        if ("corpusfile".equals(fileDimension)){
            List<String> fileIdsBySystemId = corpusSliceDocumentService.getFileIdsBySystemId(id);
            if (CollectionUtil.isNotEmpty(fileIdsBySystemId)){
                List<String> chunkId = corpusSliceSegmentService.lambdaQuery().select(CorpusSliceSegment::getId)
                        .in(CorpusSliceSegment::getDocumentId, fileIdsBySystemId).list()
                        .stream().map(CorpusSliceSegment::getId).collect(Collectors.toList());
                combineDTO.setChunkIds(chunkId);
            }
        }

        if ("knowledgefile".equals(fileDimension)) {
            List<CorpusScopeRule> list = corpusScopeRuleService.lambdaQuery()
                    .select(CorpusScopeRule::getValue)
                    .eq(CorpusScopeRule::getSystemId, id)
                    .list();

            List<String> tagValues = list.stream()
                    .map(CorpusScopeRule::getValue)
                    .filter(value -> value != null && !value.isEmpty())
                    .flatMap(value -> Arrays.stream(value.split(",")))
                    .collect(Collectors.toList());
            List<String> chunkIds = corpusSliceSegmentService.getIdsByTags(tagValues);
            combineDTO.setChunkIds(chunkIds);

        }


        AipApiClientUtil.callApi(AipApiEnum.vector.getPath(),
                makeAipBody(OperationType.COMBINE,CHUNK,combineDTO));

    }

    @Data
    public static class CombineDTO{
        //知识库id
        private String systemId;

        //切片id
        private List<String> chunkIds;

    }
}