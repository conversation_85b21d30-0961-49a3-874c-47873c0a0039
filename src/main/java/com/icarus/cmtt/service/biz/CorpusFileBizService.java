package com.icarus.cmtt.service.biz;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.entity.CorpusDetail;
import com.icarus.cmtt.entity.CorpusFile;
import com.icarus.cmtt.enums.OperationType;
import com.icarus.cmtt.service.ICorpusDetailService;
import com.icarus.cmtt.service.ICorpusFileService;
import com.icarus.cmtt.vo.CorpusFileListVO;
import com.icarus.common.minio.MinioUtil;
import io.minio.StatObjectResponse;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
public class CorpusFileBizService  {

    public static final  String  PDF = "application/pdf";
    public static final  String  DOC = "application/msword";
    public static final  String  DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    public static final  String  XLS = "application/vnd.ms-excel";
    public static final  String  XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    public static final  String  CSV = "text/csv";
    public static final  String  TXT = "text/plain";
    public static final  String  PNG  = "image/png";
    public static final  String  JPG  = "image/jpg";
    public static final  String  JPEG  = "image/jpeg";
    public static final  String  HTML = "text/html";

    private static final Map<String, String> MIME_TYPE_MAP = new HashMap<String, String>() {{
        put(PDF, "pdf");
        put(DOC, "doc");
        put(DOCX, "docx");
        put(XLS, "xls");
        put(XLSX, "xlsx");
        put(CSV, "csv");
        put(TXT, "txt");
        put(PNG, "png");
        put(JPG, "jpg");
        put(JPEG, "jpeg");
        put(HTML, "html");
    }};

    @Value("${minio.agent-bucket-name}")
    private String bucketName;

    @Resource
    private MinioUtil minioUtil;

    @Resource
    private ICorpusDetailService corpusDetailService;

    @Resource
    private ICorpusFileService corpusFileService;

    @Resource
    private VectorBizService vectorBizService;


    public Object handle(JSONObject jsonObject) {

        OperationType operationType = OperationType.valueOf(jsonObject.getStr("operationType"));

        String bodyJson = jsonObject.getStr("bodyJson");

        String filePath = jsonObject.getStr("filePath");

       switch (operationType){
           case QUERY:
               return this.pageQuery(JSONUtil.toBean(bodyJson, PageReq.class, true));
           case UPLOAD:
               return this.upload(JSONUtil.toBean(bodyJson, CorpusFileUpload.class, true),filePath);
           default:
                throw new IllegalArgumentException("不支持的操作类型: " + operationType);
       }
    }

    public Boolean upload(CorpusFileUpload corpusFileUpload,String filePath){
        if (corpusFileUpload == null|| StrUtil.isBlank(filePath)){
            throw new RuntimeException("参数错误");
        }
        StatObjectResponse fileInfo = minioUtil.getFileInfo(bucketName, filePath);
        if (fileInfo== null){
            throw new RuntimeException("文件不存在");
        }
        String corpusId = corpusFileUpload.getCorpusId();
        CorpusDetail corpusDetail = corpusDetailService.getOne(Wrappers.<CorpusDetail>lambdaQuery()
                .select(CorpusDetail::getId, CorpusDetail::getName).eq(CorpusDetail::getId, corpusId));
        if (corpusDetail==null){
            throw new RuntimeException("语料不存在");
        }
        CorpusFile corpusFile = new CorpusFile();
        String fileId = IdUtil.objectId();
        corpusFile.setCorpusId(corpusId);
        corpusFile.setId(fileId);
        corpusFile.setCorpusName(corpusDetail.getName());
        corpusFile.setCreateTime(LocalDateTime.now());
        corpusFile.setUpdateTime(LocalDateTime.now());
        corpusFile.setMinioFileKey(filePath);
        corpusFile.setName(getLastSegment(filePath,'/'));
        corpusFile.setType(fileInfo.contentType());
        corpusFile.setFileSize(Math.toIntExact(fileInfo.size()));
        boolean save = corpusFileService.save(corpusFile);
        //异步调用上传文件存储服务
        if(save){
            log.info("保存语料文件到向量库，文件ID:{}", fileId);
            CompletableFuture.runAsync(() -> {
                vectorBizService.createFile(fileId);
            });
        }
        return save;
    }

    public Page<CorpusFileListVO> pageQuery(PageReq req){
        Page<CorpusFile> page = new Page<>(req.getPageNo(), req.getPageSize());
        Page<CorpusFile> corpusFilePage = corpusFileService.page(page, Wrappers.<CorpusFile>lambdaQuery().eq(CorpusFile::getCorpusId, req.getCorpusId()));
        ArrayList<CorpusFileListVO> corpusFileListVOS = new ArrayList<>();
        for (CorpusFile record : corpusFilePage.getRecords()) {
            CorpusFileListVO corpusFileListVO = new CorpusFileListVO();
            corpusFileListVO.setCorpusId(record.getCorpusId());
            corpusFileListVO.setId(record.getId());
            corpusFileListVO.setName(record.getName());
            corpusFileListVO.setType(convertMimeType(record.getType()));
            corpusFileListVO.setUpdateTime(record.getUpdateTime());
            corpusFileListVO.setMinioFileKey(record.getMinioFileKey());
            corpusFileListVO.setFileSize(formatFileSize(record.getFileSize()));
            corpusFileListVO.setProperty(getProperty(corpusFileListVO.getType()));
            corpusFileListVOS.add(corpusFileListVO);
        }
        Page<CorpusFileListVO> result = new Page<>(req.getPageNo(), req.pageSize, corpusFilePage.getTotal());
        result.setRecords(corpusFileListVOS);
        return result;
    }


    public  String getProperty(String type){
        if ("pdf".equals(type)||"doc".equals(type)||"docx".equals(type)||"txt".equals(type)||"html".equals(type)){
            return "文档";
        }
        if ("xls".equals(type)||"xlsx".equals(type)||"csv".equals(type)){
            return "表格";
        }
        if ("png".equals(type)||"jpg".equals(type)||"bmp".equals(type)){
            return "图片";
        }
        return "";

    }

    private String convertMimeType(String type) {
        if (type == null || type.isEmpty()) {
            return ""; // 避免空指针异常
        }

        String result = MIME_TYPE_MAP.get(type.toLowerCase());
        return result != null ? result : ""; // 使用默认值代替null
    }

    @Data
    public static class CorpusFileUpload {

        //对应语料id
        private String corpusId;

    }

    public String getLastSegment(String path,Character logo) {
        if (path == null || path.isEmpty()) {
            return path;
        }
        // 查找最后一个斜杠的位置
        int lastSlashIndex = path.lastIndexOf(logo);

        // 如果找到斜杠，返回斜杠后的部分
        if (lastSlashIndex >= 0) {
            return path.substring(lastSlashIndex + 1);
        }

        // 没有斜杠则返回原字符串
        return path;
    }

    public static String formatFileSize(int sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + "B";
        }

        double sizeInKB = sizeInBytes / 1024.0;
        if (sizeInKB < 1024) {
            return String.format("%.1fKB", sizeInKB);
        }

        double sizeInMB = sizeInKB / 1024.0;
        return String.format("%.1fMB", sizeInMB);
    }



    @Data
    public static class PageReq{
        private  Integer pageNo;
        private  Integer pageSize;
        private  String  corpusId;
    }


}
