package com.icarus.cmtt.service.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.cmtt.dto.corpus.sliceconfigdir.DirectoryCreateDTO;
import com.icarus.cmtt.dto.corpus.sliceconfigdir.DirectoryQueryDTO;
import com.icarus.cmtt.dto.corpus.sliceconfigdir.DirectoryUpdateDTO;
import com.icarus.cmtt.entity.CorpusSliceConfigDirectory;
import com.icarus.cmtt.service.ICorpusSliceConfigDirectoryService;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 语料切片配置目录业务服务类
 */
@Service
public class CorpusSliceConfigDirectoryBizService {
    @Resource
    private ICorpusSliceConfigDirectoryService corpusSliceConfigDirectoryService;

    /**
     * 查询语料切片配置目录
     *
     * @return 结果
     */
    public Object querySliceConfigDirectory(DirectoryQueryDTO request) {
        if (request.getIsBrief() == null || !request.getIsBrief()) {
            List<CorpusSliceConfigDirectory> dirs = corpusSliceConfigDirectoryService.list();
            if (StringUtils.isNotBlank(request.getId())) {
                return buildTree(dirs, request.getId());
            }
            return buildTree(dirs, null);
        }
        if (StringUtils.isNotBlank(request.getId())) {
            return corpusSliceConfigDirectoryService.lambdaQuery().eq(CorpusSliceConfigDirectory::getParentId,
                    request.getId()).list();
        }
        return corpusSliceConfigDirectoryService.lambdaQuery().eq(CorpusSliceConfigDirectory::getParentId, null).list();
    }

    private List<CorpusSliceConfigDirectoryTree> buildTree(List<CorpusSliceConfigDirectory> directories,
                                                           String rootId) {
        // 将目录列表转换为以ID为键的Map
        Map<String, CorpusSliceConfigDirectoryTree> directoryMap = new HashMap<>();
        directories.forEach(dir -> directoryMap.put(dir.getId(), new CorpusSliceConfigDirectoryTree(dir)));

        // 根目录列表
        List<CorpusSliceConfigDirectoryTree> rootDirectories = new ArrayList<>();

        // 构建树形结构
        directories.forEach(dir -> {
            CorpusSliceConfigDirectoryTree tree = directoryMap.get(dir.getId());
            if (Objects.equals(dir.getParentId(), rootId)) {
                // 为根目录
                rootDirectories.add(tree);
            } else {
                // 否则找到父目录并添加到其子目录中
                CorpusSliceConfigDirectoryTree parentTree = directoryMap.get(dir.getParentId());
                if (parentTree != null) {
                    parentTree.getChildren().add(tree);
                }
            }
        });

        return rootDirectories;
    }

    /**
     * 创建语料切片配置目录
     *
     * @return 创建结果
     */
    public Object createSliceConfigDirectory(DirectoryCreateDTO request) {
        CorpusSliceConfigDirectory configDirectory = new CorpusSliceConfigDirectory();
        configDirectory.setId(IdUtil.objectId());
        configDirectory.setName(request.getName());
        configDirectory.setParentId(request.getParentId());
        corpusSliceConfigDirectoryService.save(configDirectory);
        return configDirectory;
    }

    /**
     * 更新语料切片配置目录
     *
     * @return 更新结果
     */
    public Object updateSliceConfigDirectory(DirectoryUpdateDTO request) {
        CorpusSliceConfigDirectory configDirectory = corpusSliceConfigDirectoryService.getById(request.getId());
        if (configDirectory == null) {
            throw new RuntimeException("目录id不存在");
        }
        configDirectory.setName(request.getName());
        configDirectory.setParentId(request.getParentId());
        corpusSliceConfigDirectoryService.updateById(configDirectory);
        return configDirectory;
    }

    /**
     * 删除语料切片配置目录及其所有子目录
     *
     * @param id 目录ID
     * @return 删除结果
     */
    public Object deleteSliceConfigDirectory(String id) {
        // 查询所有子目录及其后代目录
        List<String> allIds = new ArrayList<>();
        collectAllSubDirectoryIds(id, allIds);

        // 添加当前目录ID
        allIds.add(id);

        // 删除所有目录
        corpusSliceConfigDirectoryService.removeByIds(allIds);
        Map<String, List<String>> result = new HashMap<>();
        result.put("ids", allIds);
        return result;
    }

    /**
     * 递归收集所有子目录ID
     *
     * @param parentId 父目录ID
     * @param allIds   所有子目录ID集合
     */
    private void collectAllSubDirectoryIds(String parentId, List<String> allIds) {
        List<CorpusSliceConfigDirectory> subDirectories = corpusSliceConfigDirectoryService.list(new LambdaQueryWrapper<CorpusSliceConfigDirectory>().eq(CorpusSliceConfigDirectory::getParentId, parentId));
        for (CorpusSliceConfigDirectory subDirectory : subDirectories) {
            allIds.add(subDirectory.getId());
            collectAllSubDirectoryIds(subDirectory.getId(), allIds);
        }
    }

    @Getter
    @Setter
    public static class CorpusSliceConfigDirectoryTree extends CorpusSliceConfigDirectory {
        private List<CorpusSliceConfigDirectoryTree> children;

        public CorpusSliceConfigDirectoryTree(CorpusSliceConfigDirectory directory) {
            BeanUtils.copyProperties(directory, this);
            this.children = new ArrayList<>();
        }
    }
}