package com.icarus.cmtt.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.dto.CorpusSearchDTO;
import com.icarus.cmtt.entity.*;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.util.SetUtil;
import com.icarus.cmtt.vo.CorpusSearchVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CorpusSearchBizService {
    @Resource
    private ICorpusDatabaseService corpusDatabaseService;
    @Resource
    private ICorpusDetailService corpusDetailService;
    @Resource
    private ICorpusCollectionConfigService corpusCollectionConfigService;
    @Resource
    private ICorpusTagsService corpusTagsService;
    @Resource
    private ICorpusDictionaryService corpusDictionaryService;

    public Page<CorpusSearchVO> searchCorpus(CorpusSearchDTO properties) {
        Set<String> scopeIds = matchScope(properties);
        log.info("目录搜索结果：{}", scopeIds);
        Set<String> keywordIds = matchKeyword(properties);
        log.info("关键词搜索结果：{}", keywordIds);
        Set<String> attributeIds = matchAttribute(properties);
        log.info("属性搜索结果：{}", attributeIds);
        Set<String> tagIds = matchTag(properties);
        log.info("标签搜索结果：{}", tagIds);
        Set<String> unionIds = SetUtil.intersectAll(scopeIds, keywordIds, attributeIds, tagIds);
        log.info("整体搜索结果：{}", unionIds);
        if (unionIds != null && unionIds.isEmpty()) {
            return new Page<>(properties.getPageNo(), properties.getPageSize());
        }
        LambdaQueryWrapper<CorpusDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CorpusDetail::getSubject, "FINANCIAL_COMMON").orderByDesc(CorpusDetail::getUpdateTime);
        if (unionIds != null) {
            List<String> corpusIds = new ArrayList<>(unionIds);
            wrapper.in(CorpusDetail::getId, corpusIds);
        }
        Page<CorpusDetail> page = new Page<>(properties.getPageNo(), properties.getPageSize());
        Page<CorpusDetail> corpusDetailPage = corpusDetailService.page(page, wrapper);
        Map<String, String> tagMap =
                corpusTagsService.lambdaQuery().list().stream()
                        .collect(Collectors.toMap(c -> c.getId().toString(), CorpusTags::getName));
        Set<String> detailIds = corpusDetailPage.getRecords().stream().map(CorpusDetail::getCorpusCollectionConfigId).collect(Collectors.toSet());
        Map<String, CorpusCollectionConfig> configMap =
                corpusCollectionConfigService.lambdaQuery().select(CorpusCollectionConfig::getId,
                                CorpusCollectionConfig::getCollectionPath, CorpusCollectionConfig::getSourceEntity)
                        .in(CorpusCollectionConfig::getId, detailIds).list().stream()
                        .collect(Collectors.toMap(CorpusCollectionConfig::getId, Function.identity()));
        Page<CorpusSearchVO> result = new Page<>(page.getCurrent(), page.getSize(), corpusDetailPage.getTotal());
        List<CorpusSearchVO> voList = new ArrayList<>();
        for (CorpusDetail record : corpusDetailPage.getRecords()) {
            CorpusSearchVO vo = new CorpusSearchVO(record);
            vo.setCollectionPath(configMap.get(record.getCorpusCollectionConfigId()).getCollectionPath());
            vo.setSourceEntity(corpusDictionaryService.getSourceEntityPathById(configMap.get(record.getCorpusCollectionConfigId()).getSourceEntity()));
            vo.setTags(Arrays.stream(record.getTags()).map(tagMap::get).filter(Objects::nonNull).toArray(String[]::new));
            voList.add(vo);
        }
        result.setRecords(voList);
        return result;
    }

    public Set<String> matchTag(CorpusSearchDTO properties) {
        List<CorpusSearchDTO.BusinessTag> tags = properties.getBusinessTags();
        if (tags == null || tags.isEmpty()) {
            return null;
        }

        Set<String> tagIds = new HashSet<>();
        for (CorpusSearchDTO.BusinessTag tag : tags) {
            switch (tag.getMatchMode()) {
                case OR:
                    tagIds.addAll(queryTagCondition(tag));
                    break;
                case AND:
                    tagIds.retainAll(queryTagCondition(tag));
                    break;
                default:
                    break;
            }
        }
        return tagIds;
    }


    private Set<String> queryTagCondition(CorpusSearchDTO.BusinessTag tag) {
        LambdaQueryWrapper<CorpusDetail> wrapper = new LambdaQueryWrapper<>();
        switch (tag.getCondition()) {
            case CONTAIN:
                wrapper.apply("{0} = ANY(tags)", tag.getValue());
                return corpusDetailService.list(wrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case NOT_CONTAIN:
                wrapper.apply("NOT {0} = ANY(tags)", tag.getValue());
                return corpusDetailService.list(wrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            default:
                return new HashSet<>();
        }
    }

    private Set<String> matchAttribute(CorpusSearchDTO properties) {
        CorpusSearchDTO.BasicAttribute basicAttributes = properties.getBasicAttributes();
        if (basicAttributes == null || BeanUtil.isEmpty(basicAttributes)) {
            return null;
        }

        Set<String> publishDateIds = matchPublishDate(basicAttributes);
        Set<String> publishOrgIds = matchPublishOrg(basicAttributes);
        Set<String> versionStateIds = matchVersionState(basicAttributes);

        return SetUtil.intersectAll(publishDateIds, publishOrgIds, versionStateIds);
    }

    private Set<String> matchVersionState(CorpusSearchDTO.BasicAttribute basicAttributes) {
        if (StringUtils.isBlank(basicAttributes.getVersionState())) {
            return null;
        }
        LambdaQueryWrapper<CorpusDetail> wrapper = new LambdaQueryWrapper<>();
        switch (basicAttributes.getVersionState()) {
            case "valid":
                wrapper.and(w -> w.eq(CorpusDetail::getVersionIdentity, "valid"));
                break;
            case "invalid":
                wrapper.and(w -> w.eq(CorpusDetail::getVersionIdentity, "invalid"));
                break;
            case "all":
            default:
                break;
        }
        return corpusDetailService.list(wrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
    }

    private Set<String> matchPublishOrg(CorpusSearchDTO.BasicAttribute basicAttributes) {
        if (basicAttributes.getPublishOrg() == null || basicAttributes.getPublishOrg().isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<CorpusCollectionConfig> wrapper = new LambdaQueryWrapper<>();
        for (CorpusSearchDTO.PublishOrg publishOrg : basicAttributes.getPublishOrg()) {
            switch (publishOrg.getMatchMode()) {
                case OR:
                    wrapper.or(w -> {
                        switch (publishOrg.getCondition()) {
                            case EQUAL:
                                w.eq(CorpusCollectionConfig::getSourceEntity, publishOrg.getOrgCode());
                                break;
                            case NOT_EQUAL:
                                w.ne(CorpusCollectionConfig::getSourceEntity, publishOrg.getOrgCode());
                                break;
                        }
                    });
                    break;
                case AND:
                    wrapper.and(w -> {
                        switch (publishOrg.getCondition()) {
                            case EQUAL:
                                w.eq(CorpusCollectionConfig::getSourceEntity, publishOrg.getOrgCode());
                                break;
                            case NOT_EQUAL:
                                w.ne(CorpusCollectionConfig::getSourceEntity, publishOrg.getOrgCode());
                                break;
                        }
                    });
                    break;
                default:
                    break;
            }
        }
        List<String> configIds = corpusCollectionConfigService.list(wrapper).stream().map(CorpusCollectionConfig::getId).collect(Collectors.toList());
        return corpusDetailService.lambdaQuery().in(CorpusDetail::getCorpusCollectionConfigId, configIds).list().stream().map(CorpusDetail::getId).collect(Collectors.toSet());
    }

    private Set<String> matchPublishDate(CorpusSearchDTO.BasicAttribute basicAttributes) {
        if (basicAttributes.getPublishDate() == null || basicAttributes.getPublishDate().isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<CorpusDetail> wrapper = new LambdaQueryWrapper<>();
        for (CorpusSearchDTO.PublishDate publishDate : basicAttributes.getPublishDate()) {
            switch (publishDate.getMatchMode()) {
                case OR:
                    wrapper.or(w -> {
                        switch (publishDate.getCondition()) {
                            case CONTAIN:
                                w.between(CorpusDetail::getPublishDate, publishDate.getStartDate(), publishDate.getEndDate());
                                break;
                            case NOT_CONTAIN:
                                w.notBetween(CorpusDetail::getPublishDate, publishDate.getStartDate(), publishDate.getEndDate());
                                break;
                        }
                    });
                    break;
                case AND:
                    wrapper.and(w -> {
                        switch (publishDate.getCondition()) {
                            case CONTAIN:
                                w.between(CorpusDetail::getPublishDate, publishDate.getStartDate(), publishDate.getEndDate());
                                break;
                            case NOT_CONTAIN:
                                w.notBetween(CorpusDetail::getPublishDate, publishDate.getStartDate(), publishDate.getEndDate());
                                break;
                        }
                    });
                    break;
                default:
                    break;
            }
        }
        return corpusDetailService.list(wrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
    }


    private Set<String> matchKeyword(CorpusSearchDTO properties) {
        if (properties.getKeywords() == null || properties.getKeywords().isEmpty()) {
            return null;
        }
        Set<String> corpusIds = new HashSet<>();
        for (CorpusSearchDTO.Keyword keyword : properties.getKeywords()) {
            switch (keyword.getMatchMode()) {
                case OR:
                    corpusIds.addAll(queryKeywordCondition(keyword));
                    break;
                case AND:
                    corpusIds.retainAll(queryKeywordCondition(keyword));
                    break;
                default:
                    break;
            }
        }
        return corpusIds;
    }

    private Set<String> matchScope(CorpusSearchDTO properties) {
        if (properties.getDirectoryScopes() == null || properties.getDirectoryScopes().isEmpty()) {
            return null;
        }
        Set<String> corpusIds = new HashSet<>();
        properties.getDirectoryScopes().forEach(scope -> {
            switch (scope.getMatchMode()) {
                case OR:
                    corpusIds.addAll(queryScopeCondition(scope));
                    break;
                case AND:
                    corpusIds.retainAll(queryScopeCondition(scope));
                    break;
                default:
                    break;
            }
        });
        return corpusIds;
    }

    private Set<String> queryKeywordCondition(CorpusSearchDTO.Keyword keyword) {
        switch (keyword.getScope()) {
            case TITLE:
                LambdaQueryWrapper<CorpusDetail> titleWrapper = new LambdaQueryWrapper<>();
                switch (keyword.getCondition()) {
                    case EQUAL:
                        titleWrapper.eq(CorpusDetail::getName, keyword.getKeyword());
                        break;
                    case CONTAIN:
                        titleWrapper.like(CorpusDetail::getName, keyword.getKeyword());
                        break;
                    case NOT_EQUAL:
                        titleWrapper.ne(CorpusDetail::getName, keyword.getKeyword());
                        break;
                    case NOT_CONTAIN:
                        titleWrapper.notLike(CorpusDetail::getName, keyword.getKeyword());
                        break;
                }
                return corpusDetailService.list(titleWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case TEXT:
                LambdaQueryWrapper<CorpusDetail> textWrapper = new LambdaQueryWrapper<>();
                switch (keyword.getCondition()) {
                    case EQUAL:
                        textWrapper.eq(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                    case CONTAIN:
                        textWrapper.like(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                    case NOT_EQUAL:
                        textWrapper.ne(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                    case NOT_CONTAIN:
                        textWrapper.notLike(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                }
                return corpusDetailService.list(textWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case SUMMARY:
                LambdaQueryWrapper<CorpusDetail> summaryWrapper = new LambdaQueryWrapper<>();
                switch (keyword.getCondition()) {
                    case EQUAL:
                        summaryWrapper.eq(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                    case CONTAIN:
                        summaryWrapper.like(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                    case NOT_EQUAL:
                        summaryWrapper.ne(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                    case NOT_CONTAIN:
                        summaryWrapper.notLike(CorpusDetail::getDescription, keyword.getKeyword());
                        break;
                }
                return corpusDetailService.list(summaryWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case KEYWORD:
                LambdaQueryWrapper<CorpusDetail> keywordWrapper = new LambdaQueryWrapper<>();
                switch (keyword.getCondition()) {
                    case EQUAL:
                        keywordWrapper.eq(CorpusDetail::getKeyWords, keyword.getKeyword());
                        break;
                    case CONTAIN:
                        keywordWrapper.like(CorpusDetail::getKeyWords, keyword.getKeyword());
                        break;
                    case NOT_EQUAL:
                        keywordWrapper.ne(CorpusDetail::getKeyWords, keyword.getKeyword());
                        break;
                    case NOT_CONTAIN:
                        keywordWrapper.notLike(CorpusDetail::getKeyWords, keyword.getKeyword());
                        break;
                }
                return corpusDetailService.list(keywordWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            default:
                return new HashSet<>();
        }
    }

    private Set<String> queryScopeCondition(CorpusSearchDTO.DirectoryScope scope) {
        switch (scope.getCondition()) {
            case EQUAL:
                LambdaQueryWrapper<CorpusDetail> equalCorpusWrapper = new LambdaQueryWrapper<>();
                equalCorpusWrapper.eq(CorpusDetail::getCorpusDatabaseId, scope.getCorpusDirectory());
                return corpusDetailService.list(equalCorpusWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case CONTAIN:
                List<String> containDatabaseIds = queryDatabaseContainIds(scope.getCorpusDirectory());
                LambdaQueryWrapper<CorpusDetail> containCorpusWrapper = new LambdaQueryWrapper<>();
                containCorpusWrapper.in(CorpusDetail::getCorpusDatabaseId, containDatabaseIds);
                return corpusDetailService.list(containCorpusWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case NOT_EQUAL:
                LambdaQueryWrapper<CorpusDetail> notEqualCorpusWrapper = new LambdaQueryWrapper<>();
                notEqualCorpusWrapper.ne(CorpusDetail::getCorpusDatabaseId, scope.getCorpusDirectory());
                return corpusDetailService.list(notEqualCorpusWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            case NOT_CONTAIN:
                List<String> databaseIds = queryDatabaseContainIds(scope.getCorpusDirectory());
                LambdaQueryWrapper<CorpusDetail> notContainCorpusWrapper = new LambdaQueryWrapper<>();
                notContainCorpusWrapper.notIn(CorpusDetail::getCorpusDatabaseId, databaseIds);
                return corpusDetailService.list(notContainCorpusWrapper).stream().map(CorpusDetail::getId).collect(Collectors.toSet());
            default:
                return new HashSet<>();
        }
    }

    List<String> queryDatabaseContainIds(String id) {
        Queue<String> queue = new LinkedList<>();
        queue.add(id);
        List<String> databaseIds = new ArrayList<>();
        databaseIds.add(id);
        while (!queue.isEmpty()) {
            String directoryId = queue.poll();
            LambdaQueryWrapper<CorpusDatabase> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CorpusDatabase::getParentId, directoryId);
            List<String> ids =
                    corpusDatabaseService.list(wrapper).stream().map(CorpusDatabase::getId).collect(Collectors.toList());
            queue.addAll(ids);
            databaseIds.add(directoryId);
        }
        return databaseIds;
    }
}
