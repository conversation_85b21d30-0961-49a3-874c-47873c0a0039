package com.icarus.cmtt.service.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.cmtt.dto.CorpusDetailDTO;
import com.icarus.cmtt.entity.CorpusCollectionConfig;
import com.icarus.cmtt.entity.CorpusDetail;
import com.icarus.cmtt.entity.CorpusFile;
import com.icarus.cmtt.entity.CorpusTags;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.vo.CorpusDetailVO;
import com.icarus.common.minio.MinioUtil;
import io.minio.StatObjectResponse;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class CorpusDetailBizService {
    @Resource
    private ICorpusDetailService corpusDetailService;

    @Resource
    private ICorpusCollectionConfigService corpusCollectionConfigService;

    @Resource
    private ICorpusTagsService corpusTagsService;

    @Resource
    private ICorpusFileService corpusFileService;

    @Resource
    private ICorpusDictionaryService corpusDictionaryService;

    @Resource
    private VectorBizService vectorBizService;

    @Resource
    private CorpusSliceTaskBizService sliceTaskBizService;

    @Resource
    private MinioUtil minioUtil;

    @Value("${minio.agent-bucket-name}")
    private String bucketName;

    public Object queryCorpusDetail(CorpusDetailDTO properties) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isBlank(properties.getId())) {
            throw new RuntimeException("语料ID不能为空");
        }
        CorpusDetail detail = corpusDetailService.lambdaQuery().eq(CorpusDetail::getId, properties.getId()).one();
        if (detail == null) {
            throw new RuntimeException("语料不存在");
        }
        CorpusDetailVO detailVo = new CorpusDetailVO(detail);
        setConfigAttr(detailVo);
        Map<String, String> tagMap =
                corpusTagsService.lambdaQuery().list().stream()
                        .collect(Collectors.toMap(c -> c.getId().toString(), CorpusTags::getName));
        detailVo.setTags(Arrays.stream(detailVo.getTags()).map(tagMap::get).toArray(String[]::new));
        result.put("detail", detailVo);
        return result;
    }

    private void setConfigAttr(CorpusDetailVO detailVo) {
        CorpusCollectionConfig config = corpusCollectionConfigService.lambdaQuery().eq(CorpusCollectionConfig::getId, detailVo.getCorpusCollectionConfigId()).one();
        detailVo.setCollectionPath(config.getCollectionPath());
        String path = corpusDictionaryService.getSourceEntityPathById(config.getSourceEntity());
        detailVo.setSourceEntity(path);
        detailVo.setSourceInstitutionUrl(config.getSourceInstitutionUrl());
        detailVo.setCollectionPathUrl(config.getCollectionPathUrl());
        detailVo.setCollectionDetailPath(config.getCollectionDetailPath());
        detailVo.setSourceInstitution(path.split("/")[0]);
        detailVo.setSubSourceInstitution(path.split("/")[1]);
    }

    public Object updateCorpusDetail(CorpusDetailDTO properties) {
        if (StringUtils.isBlank(properties.getId())) {
            throw new RuntimeException("语料ID不能为空");
        }
        // 获取现有数据库配置
        CorpusDetail existingCorpusDetail =
                corpusDetailService.lambdaQuery().eq(CorpusDetail::getId, properties.getId()).one();

        if (existingCorpusDetail == null) {
            throw new RuntimeException("语料不存在");
        }

        // 更新语料库
        CorpusDetailDTO.BaseAttributes baseAttributes = properties.getBaseAttributes();
        updateDetail(baseAttributes, existingCorpusDetail);
        // 更新采集配置
        updateCollectConfig(existingCorpusDetail, baseAttributes);

        // 返回更新后的数据库配置
        Map<String, Object> result = new HashMap<>();
        result.put("id", existingCorpusDetail.getId());
        return result;
    }

    private void updateCollectConfig(CorpusDetail existingCorpusDetail, CorpusDetailDTO.BaseAttributes baseAttributes) {
        if (StringUtils.isBlank(existingCorpusDetail.getCorpusCollectionConfigId())) {
            return;
        }
        CorpusCollectionConfig config =
                corpusCollectionConfigService.getById(existingCorpusDetail.getCorpusCollectionConfigId());
        if (config == null) {
            return;
        }
        if (StringUtils.isNotBlank(baseAttributes.getSourceInstitutionUrl())) {
            config.setSourceInstitutionUrl(baseAttributes.getSourceInstitutionUrl());
        }
        if (StringUtils.isNotBlank(baseAttributes.getCollectionPath())) {
            config.setCollectionPath(baseAttributes.getCollectionPath());
        }
        if (StringUtils.isNotBlank(baseAttributes.getCollectionPathUrl())) {
            config.setCollectionPathUrl(baseAttributes.getCollectionPathUrl());
        }
        if (StringUtils.isNotBlank(baseAttributes.getCollectionDetailPath())) {
            config.setCollectionDetailPath(baseAttributes.getCollectionDetailPath());
        }
        config.setUpdateTime(LocalDateTime.now());
        corpusCollectionConfigService.updateById(config);
    }

    private void updateDetail(CorpusDetailDTO.BaseAttributes baseAttributes, CorpusDetail existingCorpusDetail) {
        if (StringUtils.isNotBlank(baseAttributes.getName())) {
            existingCorpusDetail.setName(baseAttributes.getName());
        }
        if (StringUtils.isNotBlank(baseAttributes.getDescription())) {
            existingCorpusDetail.setDescription(baseAttributes.getDescription());
        }
        if (StringUtils.isNotBlank(baseAttributes.getOfficialNumber())) {
            existingCorpusDetail.setOfficialNumber(baseAttributes.getOfficialNumber());
        }
        if (StringUtils.isNotBlank(baseAttributes.getOwner())) {
            existingCorpusDetail.setOwner(baseAttributes.getOwner());
        }
        if (StringUtils.isNotBlank(baseAttributes.getPermission())) {
            existingCorpusDetail.setPermission(baseAttributes.getPermission());
        }
        if (StringUtils.isNotBlank(baseAttributes.getCorpusDatabaseId())) {
            existingCorpusDetail.setCorpusDatabaseId(baseAttributes.getCorpusDatabaseId());
        }
        if (baseAttributes.getBusinessTag() != null) {
            existingCorpusDetail.setTags(baseAttributes.getBusinessTag());
        }
        existingCorpusDetail.setUpdateTime(LocalDateTime.now());

        // 保存更新
        corpusDetailService.updateById(existingCorpusDetail);
    }

    public Object deleteCorpusDetail(CorpusDetailDTO properties) {
        if (StringUtils.isBlank(properties.getId())) {
            throw new RuntimeException("语料ID不能为空");
        }
        LambdaQueryWrapper<CorpusDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CorpusDetail::getId, properties.getId());
        corpusDetailService.remove(wrapper);
        Map<String, Object> result = new HashMap<>();
        result.put("id", properties.getId());
        return result;
    }

    public Object insertCorpusDetail(CorpusDetailDTO properties) {
        if (StringUtils.isBlank(properties.getBaseAttributes().getCorpusDatabaseId())) {
            throw new RuntimeException("基础属性不能为空");
        }
        CorpusDetailDTO.BaseAttributes baseAttributes = properties.getBaseAttributes();
        CorpusCollectionConfig config = buildCorpusCollectConfig(baseAttributes);
        corpusCollectionConfigService.save(config);
        CorpusDetail detail = buildCorpusDetail(baseAttributes, config);
        corpusDetailService.save(detail);
        for (String filePath : baseAttributes.getFileList()) {
            StatObjectResponse fileInfo = minioUtil.getFileInfo(bucketName, filePath);
            if (minioUtil.getFileInfo(bucketName, filePath) == null) continue;
            CorpusFile corpusFile = buildCorpusFile(filePath, detail, fileInfo);
            corpusFileService.save(corpusFile);
            CompletableFuture.runAsync(()-> vectorBizService.createFile(corpusFile.getId()));
        }
        CompletableFuture.runAsync(()-> sliceTaskBizService.create(detail));
        return detail;
    }

    private static CorpusFile buildCorpusFile(String filePath, CorpusDetail detail, StatObjectResponse fileInfo) {
        CorpusFile corpusFile = new CorpusFile();
        corpusFile.setId(IdUtil.objectId());
        corpusFile.setCorpusId(detail.getId());
        corpusFile.setCorpusName(detail.getName());
        corpusFile.setCreateTime(LocalDateTime.now());
        corpusFile.setUpdateTime(LocalDateTime.now());
        corpusFile.setMinioFileKey(filePath);
        corpusFile.setName(Arrays.stream(filePath.split("/")).reduce((a, b) -> b).orElse(filePath));
        corpusFile.setType(fileInfo.contentType());
        corpusFile.setFileSize(Math.toIntExact(fileInfo.size()));
        return corpusFile;
    }

    private static CorpusCollectionConfig buildCorpusCollectConfig(CorpusDetailDTO.BaseAttributes baseAttributes) {
        CorpusCollectionConfig config = new CorpusCollectionConfig();
        config.setId(IdUtil.objectId());
        config.setCollectionChannel("本地电脑");
        config.setCollectionType("手工上传");
        config.setSubject("FINANCIAL_COMMON");
        config.setSourceEntity(baseAttributes.getSourceEntity());
        config.setExecuteWay(baseAttributes.getExecuteWay());
        config.setExecuteDetail(baseAttributes.getExecuteDetail());
        config.setOwner(baseAttributes.getOwner());
        config.setPermission(baseAttributes.getPermission());
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        return config;
    }

    private static CorpusDetail buildCorpusDetail(CorpusDetailDTO.BaseAttributes baseAttributes, CorpusCollectionConfig config) {
        CorpusDetail detail = new CorpusDetail();
        detail.setId(IdUtil.objectId());
        detail.setCorpusDatabaseId(baseAttributes.getCorpusDatabaseId());
        detail.setName(baseAttributes.getName());
        detail.setDescription(baseAttributes.getDescription());
        detail.setOfficialNumber(baseAttributes.getOfficialNumber());
        detail.setTags(baseAttributes.getBusinessTag());
        detail.setOwner(baseAttributes.getOwner());
        detail.setPermission(baseAttributes.getPermission());
        detail.setPublishDate(LocalDateTime.now());
        detail.setSubject("FINANCIAL_COMMON");
        detail.setCorpusCollectionConfigId(config.getId());
        detail.setCreateTime(LocalDateTime.now());
        detail.setUpdateTime(LocalDateTime.now());
        return detail;
    }
}
