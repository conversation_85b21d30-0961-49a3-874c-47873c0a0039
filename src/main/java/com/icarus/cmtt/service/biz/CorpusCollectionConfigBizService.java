package com.icarus.cmtt.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.dto.CorpusCollectionConfigDTO;
import com.icarus.cmtt.entity.CorpusCollectionConfig;
import com.icarus.cmtt.entity.CrawlSetting;
import com.icarus.cmtt.entity.EnumDictionary;
import com.icarus.cmtt.enums.OperationType;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.vo.CollectConfigListVO;
import jakarta.annotation.Resource;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class CorpusCollectionConfigBizService{
    @Resource
    private ICorpusCollectionConfigService collectionConfigService;

    @Resource
    private IEnumDictionaryService enumDictionaryService;

    @Resource
    private ICrawlSettingService crawlSettingService;

    @Resource
    private ICorpusDictionaryService corpusDictionaryService;

    @Resource
    private ICorpusTagsService corpusTagsService;

    private final static String CRAWL = "网络收集器";

    public Object handle(JSONObject jsonObject) {

        OperationType operationType = OperationType.valueOf(jsonObject.getStr("operationType"));

        String bodyJson = jsonObject.getStr("bodyJson");
         switch (operationType) {
             case CREATE :
            // 创建操作：将JSON转换为实体对象，并调用create方法
             return   this.create(JSONUtil.toBean(bodyJson, CorpusCollectionConfigDTO.class, true));
             case DELETE :
            // 删除操作：将JSON转换为ID，并调用delete方法
                 return  this.delete(JSONUtil.toBean(bodyJson, IdItem.class, true));
             case UPDATE :
            // 更新操作：将JSON转换为实体对象，并调用update方法
                 return this.updateConfig(JSONUtil.toBean(bodyJson, CorpusCollectionConfigDTO.class, true));
             case GET:
            // 获取操作：将JSON转换为ID，并调用get方法
                 return this.getDetail(JSONUtil.toBean(bodyJson,IdItem.class, true));
             case QUERY:
            // 查询操作：将JSON转换为查询请求对象，并调用page方法
                 return this.pageQuery(JSONUtil.toBean(bodyJson,PageReq.class, true));
             case ENUM:
            // 获取枚举
                 return this.enumQuery(JSONUtil.toBean(bodyJson,EnumItem.class,true));

             default:
                 throw new IllegalArgumentException("不支持的操作类型: " + operationType);
        }
    }

    private List<String> enumQuery(EnumItem enumItem) {
        if (enumItem == null) {
            return null;
        }

        List<EnumDictionary> enumDictionaries = enumDictionaryService.list(Wrappers.<EnumDictionary>lambdaQuery().
                select(EnumDictionary::getValue).
                eq(EnumDictionary::getProperty, enumItem.getProperty()).orderByAsc(EnumDictionary::getId));
        return enumDictionaries.stream().map(EnumDictionary::getValue).collect(Collectors.toList());
    }


    /**
     * 分页查询目录
     *
     * @param req 分页查询请求
     * @return 分页查询结果
     */
    public Page<CollectConfigListVO> pageQuery(PageReq req) {
        // 类型转换
        // 创建分页对象
        Page<CorpusCollectionConfig> page = new Page<>(req.getPageNo(), req.getPageSize());
        // 创建查询条件
        LambdaQueryWrapper<CorpusCollectionConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(CorpusCollectionConfig::getCollectionType, "手工上传").orderByDesc(CorpusCollectionConfig::getUpdateTime);

        QueryBody queryBody = req.getQueryBody();
        //条件查询
        if ( queryBody != null) {
            wrapper.eq(queryBody.getStatus() != null, CorpusCollectionConfig::getStatus, queryBody.getStatus());
            wrapper.like(StrUtil.isNotBlank(queryBody.getCollectionType()),CorpusCollectionConfig::getCollectionType,queryBody.getCollectionType());
            wrapper.like(StrUtil.isNotBlank(queryBody.getCollectionChannel()), CorpusCollectionConfig::getCollectionChannel, queryBody.getCollectionChannel());
            wrapper.eq(StrUtil.isNotBlank(queryBody.getSourceEntity()),CorpusCollectionConfig::getSourceEntity,
                    queryBody.getSourceEntity());
            wrapper.like(StrUtil.isNotBlank(queryBody.getCollectionPathUrl()), CorpusCollectionConfig::getCollectionPathUrl, queryBody.getCollectionPathUrl());
            wrapper.like(StrUtil.isNotBlank(queryBody.getCorpusDatabasePath()), CorpusCollectionConfig::getCorpusDatabasePath, queryBody.getCorpusDatabasePath());
            if (CollectionUtil.isNotEmpty(queryBody.getCreateTime())) {
                wrapper.gt(StrUtil.isNotBlank(queryBody.getCreateTime().get(0)),
                        CorpusCollectionConfig::getCreateTime, convertStringToLocalDateTime(queryBody.getCreateTime().get(0)));
                wrapper.lt( StrUtil.isNotBlank(queryBody.getCreateTime().get(1)),
                        CorpusCollectionConfig::getCreateTime, convertStringToLocalDateTime(queryBody.getCreateTime().get(1)));
            }
            if (CollectionUtil.isNotEmpty(queryBody.getExecuteTime())) {
                wrapper.gt(StrUtil.isNotBlank(queryBody.getExecuteTime().get(0)),
                        CorpusCollectionConfig::getExecuteTime, convertStringToLocalDateTime(queryBody.getExecuteTime().get(0)));
                wrapper.lt(StrUtil.isNotBlank(queryBody.getExecuteTime().get(1)),
                        CorpusCollectionConfig::getExecuteTime, convertStringToLocalDateTime(queryBody.getExecuteTime().get(1)));
            }
            wrapper.like(StrUtil.isNotBlank(queryBody.getOwner()), CorpusCollectionConfig::getOwner, queryBody.getOwner());
            //
            if (StrUtil.isNotBlank(queryBody.getFrequency())||
                    StrUtil.isNotBlank(queryBody.getFrequencyStartTime())||
                    StrUtil.isNotBlank(queryBody.getFrequencyEndTime())){
                List<String> conditionIds = getConditionIds(queryBody.getFrequency(),queryBody.getFrequencyStartTime(),queryBody.getFrequencyEndTime());
                wrapper.in(CollectionUtil.isNotEmpty(conditionIds), CorpusCollectionConfig::getId, conditionIds);
            }
        }
        Page<CorpusCollectionConfig> pageResult = collectionConfigService.page(page, wrapper);
        ArrayList<CollectConfigListVO> listVOS = new ArrayList<>();
        for (CorpusCollectionConfig record : pageResult.getRecords()) {
            CollectConfigListVO vo = new CollectConfigListVO();
            vo.setId(record.getId());
            vo.setStatus(record.getStatus());
            vo.setCollectionChannel(record.getCollectionChannel());
            vo.setSourceEntity(corpusDictionaryService.getSourceEntityPathById(record.getSourceEntity()));
            vo.setCollectionPathUrl(record.getCollectionPathUrl());
            vo.setCorpusDatabasePath(record.getCorpusDatabasePath());
            vo.setCreateTime(record.getCreateTime());
            vo.setExecuteTime(record.getExecuteTime());
            vo.setFrequency(JSONUtil.parseArray(record.getFrequency()));
            vo.setOwner(record.getOwner());
            vo.setCollectionType(record.getCollectionType());
            listVOS.add(vo);
        }
        Page<CollectConfigListVO> resultPage = new Page<>(req.getPageNo(), req.getPageSize(), pageResult.getTotal());
        resultPage.setRecords(listVOS);
        // 执行分页查询
        return resultPage;
    }

    private List<String> getConditionIds(String frequency,String startTime,String endTime){
        return collectionConfigService.selectIdsByFrequency(frequency,startTime,endTime);
    }


    public CorpusCollectionConfigDTO updateConfig(CorpusCollectionConfigDTO collectionConfig) {
        if(StrUtil.isBlank(collectionConfig.getId())){
            throw new RuntimeException("语料收集器id不得为空");
        }
        CorpusCollectionConfig transfer = transfer(collectionConfig);
        boolean update = collectionConfigService.updateById(transfer);
        return  update ?collectionConfig:null;
    }


    public CorpusCollectionConfigDTO create(CorpusCollectionConfigDTO collectionConfig) {
        CorpusCollectionConfig transfer = transfer(collectionConfig);
        boolean save = collectionConfigService.save(transfer);
        return  save?collectionConfig:null;
    }

    private CorpusCollectionConfig transfer(CorpusCollectionConfigDTO req){
        CorpusCollectionConfig corpusCollectionConfig = new CorpusCollectionConfig();
        if(StringUtils.isBlank(req.getId())){
            corpusCollectionConfig.setId(IdUtil.objectId());
            corpusCollectionConfig.setCreateTime(LocalDateTime.now());
        }else {
            corpusCollectionConfig.setId(req.getId());
        }
        corpusCollectionConfig.setCollectionChannel(req.getCollectionChannel());
        corpusCollectionConfig.setSourceEntity(req.getSourceEntity());
        corpusCollectionConfig.setSourceInstitutionUrl(req.getSourceInstitutionUrl());
        corpusCollectionConfig.setCollectionType(req.getCollectionType());
        corpusCollectionConfig.setCollectionPath(req.getCollectionPath());
        corpusCollectionConfig.setCollectionDetailPath(req.getCollectionDetailPath());
        corpusCollectionConfig.setCollectionPathUrl(req.getCollectionPathUrl());
        corpusCollectionConfig.setCorpusDatabasePath(req.getCorpusDatabasePath());
        corpusCollectionConfig.setOwner(req.getOwner());
        corpusCollectionConfig.setUpdateTime(LocalDateTime.now());
        corpusCollectionConfig.setPermission(req.getPermission());
        corpusCollectionConfig.setPathAttribute(req.getPathAttribute());
        corpusCollectionConfig.setFrequency(JSONUtil.toJsonStr(req.getFrequency()));
        corpusCollectionConfig.setExecuteWay(req.getExecuteWay());
        corpusCollectionConfig.setExecuteContent(req.getExecuteContent());
        corpusCollectionConfig.setExecuteDetailId(req.getExecuteDetailId());
        corpusCollectionConfig.setExecuteDetail(getFullPath(req.getExecuteDetailId()));
        //爬虫配置校验
        if (CRAWL.equals(req.getCollectionChannel())) {
            CrawlSetting crawlSetting = crawlSettingService.getOne(Wrappers.<CrawlSetting>lambdaQuery().select(CrawlSetting::getId).eq(CrawlSetting::getCrawlUrl,req.getCollectionPathUrl()));
            if (null == crawlSetting) {
                throw new RuntimeException("对应爬虫配置不存在");
            }
            corpusCollectionConfig.setCrawlSettingId(Math.toIntExact(crawlSetting.getId()));
        }
        return corpusCollectionConfig;
    }

    private String getFullPath(String id){
        JSONArray objects = JSONUtil.parseArray(id);
        ArrayList<String> list = new ArrayList<>();
        for (Object object : objects) {
            String fullPathName = corpusTagsService.getFullPathName(object.toString());
            list.add(fullPathName);
        }
        return JSONUtil.toJsonStr( list);
    }


    public CorpusCollectionConfig getDetail(IdItem idItem) {
        if (idItem.getId()==null){
            throw new RuntimeException("语料收集器id不得为空");
        }
        return collectionConfigService.getById(idItem.getId());
    }

    public Boolean delete(IdItem idItem) {
        if (idItem.getStatus()==3){
            int deleteById = collectionConfigService.getBaseMapper().deleteById(idItem.getId());
            return deleteById>0;
        }else {
            return collectionConfigService.update(Wrappers.<CorpusCollectionConfig>lambdaUpdate()
                    .set(CorpusCollectionConfig::getStatus, idItem.getStatus())
                    .eq(CorpusCollectionConfig::getId, idItem.getId()));
        }
    }

    private static LocalDateTime convertStringToLocalDateTime(String dateStr) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析字符串并转换为 LocalDateTime (默认时间为00:00:00)
        return LocalDate.parse(dateStr, formatter).atStartOfDay();
    }


















    @Data
    public static class PageReq{
        private  Integer pageNo;
        private  Integer pageSize;
        private QueryBody queryBody;
    }

    @Data
    public static class IdItem {
        private String id;
        private Integer status;
    }

    @Data
    public static class EnumItem {
        private String property;
    }

    @Data
    private static class QueryBody {
        private String id;
        private Integer status;
        private String collectionChannel;
        private String collectionType;
        //来源主体
        private String sourceEntity;
        private String collectionPathUrl;
        private String corpusDatabasePath;
        private String frequency;
        private String frequencyEndTime;
        private String frequencyStartTime;
        private List<String> executeTime;
        private List<String> createTime;
        private String owner;
    }



}
