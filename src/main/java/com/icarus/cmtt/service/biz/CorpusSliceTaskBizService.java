package com.icarus.cmtt.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.dto.corpus.slice.*;
import com.icarus.cmtt.entity.*;
import com.icarus.cmtt.enums.AipApiEnum;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.util.AipApiClientUtil;
import com.icarus.cmtt.vo.CorpusSliceTaskVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CorpusSliceTaskBizService {
    @Resource
    private ICorpusSliceConfigService corpusSliceConfigService;

    @Resource
    private ICorpusSliceTaskService corpusSliceTaskService;

    @Resource
    private ICorpusSliceDocumentService corpusSliceDocumentService;

    @Resource
    private ICorpusFileService corpusFileService;

    @Resource
    private ICorpusSliceSegmentService corpusSliceSegmentService;

    @Resource
    private ICorpusDetailService corpusDetailService;

    @Resource
    private ICorpusTagsService corpusTagsService;

    @Resource
    private VectorBizService vectorBizService;

    @Resource
    private ICorpusDatabaseService corpusDatabaseService;

    public CorpusSliceTask create(CorpusDetail detail) {
        CorpusSliceTask task = new CorpusSliceTask();
        task.setName(detail.getName());
        task.setMode(0);
        task.setConfigId(corpusSliceConfigService.lambdaQuery().eq(CorpusSliceConfig::getStatus, 1).one().getId());
        task.setCorpusId(detail.getId());
        task.setCorpusDatabaseId(detail.getCorpusDatabaseId());
        corpusSliceTaskService.save(task);

        List<CorpusSliceDocument> documents = new ArrayList<>();
        List<CorpusFile> corpusFiles = corpusFileService.lambdaQuery().eq(CorpusFile::getCorpusId, detail.getId()).list();
        for (CorpusFile corpusFile : corpusFiles) {
            CorpusSliceDocument document = new CorpusSliceDocument();
            document.setFileId(corpusFile.getId());
            document.setSliceTaskId(task.getId());
            document.setTitle(corpusFile.getName());
            document.setImportMethod("语料库导入");
            document.setTags(detail.getTags());
            document.setFilePath(corpusFile.getMinioFileKey());
            documents.add(document);
        }
        corpusSliceDocumentService.saveBatch(documents);
        CompletableFuture.runAsync(() -> start(task));
        return task;
    }

    public CorpusSliceTask create(SliceCreateDTO request) {
        CorpusSliceTask task = new CorpusSliceTask();
        task.setName(request.getName());
        task.setMode(1);
        task.setConfigId(request.getConfigId());
        corpusSliceTaskService.save(task);

        List<CorpusSliceDocument> documents = new ArrayList<>();
        for (String filePath : request.getFileList()) {
            CorpusSliceDocument document = new CorpusSliceDocument();
            document.setSliceTaskId(task.getId());
            document.setTitle(Arrays.stream(filePath.split("/")).reduce((a, b) -> b).orElse(filePath));
            document.setImportMethod("本地上传");
            document.setFilePath(filePath);
            documents.add(document);
        }
        for (String corpusId : request.getCorpusList()) {
            CorpusDetail corpusDetail = corpusDetailService.getById(corpusId);
            List<CorpusFile> corpusFiles =
                    corpusFileService.lambdaQuery().eq(CorpusFile::getCorpusId, corpusId).list();
            for (CorpusFile corpusFile : corpusFiles) {
                CorpusSliceDocument document = new CorpusSliceDocument();
                document.setFileId(corpusFile.getId());
                document.setSliceTaskId(task.getId());
                document.setTitle(corpusFile.getName());
                document.setImportMethod("语料库导入");
                document.setTags(corpusDetail.getTags());
                document.setFilePath(corpusFile.getMinioFileKey());
                documents.add(document);
            }
        }
        corpusSliceDocumentService.saveBatch(documents);
        CompletableFuture.runAsync(() -> start(task));
        return task;
    }

    public CorpusSliceTask start(SliceStartDTO request) {
        CorpusSliceTask task = corpusSliceTaskService.getById(request.getId());
        CompletableFuture.runAsync(() -> start(task));
        return task;
    }

    private void start(CorpusSliceTask task) {
        log.info("语料切片任务开始: {}", task.getId());
        task.setStatus(1);
        task.setStartTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        corpusSliceTaskService.updateById(task);
        CorpusSliceConfig config = corpusSliceConfigService.getById(task.getConfigId());
        corpusSliceSegmentService.remove(new LambdaQueryWrapper<CorpusSliceSegment>().eq(CorpusSliceSegment::getSliceTaskId, task.getId()));
        List<CorpusSliceDocument> documents = corpusSliceDocumentService.lambdaQuery().eq(CorpusSliceDocument::getSliceTaskId, task.getId()).list();
        documents.parallelStream().forEach(document -> {
            CorpusFile file = corpusFileService.getById(document.getFileId());
            if (file != null) {
                document.setTitle(file.getName());
                document.setFilePath(file.getMinioFileKey());
            }
            document.setStatus(1);
            corpusSliceDocumentService.updateById(document);
            slice(config, document);
        });
        task.setStatus(2);
        task.setEndTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        corpusSliceTaskService.updateById(task);
    }

    private void slice(CorpusSliceConfig config, CorpusSliceDocument document) {
        Map<String, String> queryParam = new HashMap<>();
        Map<String, String> body = new HashMap<>();
        body.put("filePath", document.getFilePath());
        body.put("sliceSize", config.getSliceSize().toString());
        body.put("overlapSize", config.getOverlapSize().toString());
        String res = AipApiClientUtil.callApi(AipApiEnum.fileSlice.getPath(), queryParam, body);
        log.info("语料切片结果: {}", res);
        JSONArray contents = JSONUtil.parseObj(res).getJSONObject("data").getJSONArray("result");
        for (int i = 0; i < contents.size(); i++) {
            CorpusSliceSegment segment = new CorpusSliceSegment();
            segment.setId(IdUtil.objectId());
            segment.setDocumentId(document.getId());
            segment.setIndex(i + 1);
            segment.setCreateTime(LocalDateTime.now());
            segment.setUpdateTime(LocalDateTime.now());
            segment.setFileId(document.getFileId());
            segment.setSliceTaskId(document.getSliceTaskId());
            segment.setFileName(document.getTitle());
            String content = contents.get(i).toString();
            segment.setContent(content);
            segment.setLength(content.length());
            corpusSliceSegmentService.save(segment);
            vectorBizService.createChunk(segment);
        }
        document.setNumber(contents.size());
        document.setUpdateTime(LocalDateTime.now());
        document.setStatus(2);
        corpusSliceDocumentService.updateById(document);
    }

    public Page<CorpusSliceTaskVO> query(SliceQueryDTO request) {
        // 初始化分页参数
        Page<CorpusSliceTask> page = new Page<>(request.getPageNo(), request.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<CorpusSliceTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(CorpusSliceTask::getUpdateTime);

        SliceQueryDTO.SliceQueryBody queryBody = request.getQueryBody();
        if (queryBody != null) {
            wrapper.eq(queryBody.getMode() != null, CorpusSliceTask::getMode, queryBody.getMode())
                    .eq(queryBody.getId() != null, CorpusSliceTask::getId, queryBody.getId())
                    .eq(queryBody.getStatus() != null, CorpusSliceTask::getStatus, queryBody.getStatus())
                    .eq(StringUtils.isNotBlank(queryBody.getCorpusDatabaseId()), CorpusSliceTask::getCorpusDatabaseId, queryBody.getCorpusDatabaseId())
                    .like(StringUtils.isNotBlank(queryBody.getName()), CorpusSliceTask::getName, queryBody.getName())
                    .between(queryBody.getCreateStartTime() != null && queryBody.getCreateEndTime() != null, CorpusSliceTask::getCreateTime, queryBody.getCreateStartTime(), queryBody.getCreateEndTime())
                    .between(queryBody.getUpdateStartTime() != null && queryBody.getUpdateEndTime() != null, CorpusSliceTask::getUpdateTime, queryBody.getUpdateStartTime(), queryBody.getUpdateEndTime())
                    .between(queryBody.getStartStartTime() != null && queryBody.getStartEndTime() != null, CorpusSliceTask::getStartTime, queryBody.getStartStartTime(), queryBody.getStartEndTime())
                    .between(queryBody.getEndStartTime() != null && queryBody.getEndEndTime() != null, CorpusSliceTask::getEndTime, queryBody.getEndStartTime(), queryBody.getEndEndTime());
        } else {
            // 无查询条件时默认查询mode=0的数据
            wrapper.eq(CorpusSliceTask::getMode, 0);
        }

        // 执行分页查询
        Page<CorpusSliceTask> tasks = corpusSliceTaskService.page(page, wrapper);

        if (tasks.getRecords().isEmpty()) {
            return Page.of(tasks.getCurrent(), tasks.getSize(), tasks.getTotal());
        }

        // 提取所有需要查询的ID
        List<String> corpusIds = tasks.getRecords().stream()
                .map(CorpusSliceTask::getCorpusId)
                .distinct()
                .collect(Collectors.toList());

        List<String> databaseIds = tasks.getRecords().stream()
                .map(CorpusSliceTask::getCorpusDatabaseId)
                .distinct()
                .collect(Collectors.toList());

        List<String> configIds = tasks.getRecords().stream()
                .map(CorpusSliceTask::getConfigId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询关联数据
        Map<String, String> corpusNameMap = corpusDetailService.listByIds(corpusIds).stream()
                .collect(Collectors.toMap(CorpusDetail::getId, CorpusDetail::getName, (a, b) -> a));

        Map<String, String> databasePathMap = corpusDatabaseService.getFullPathMap(databaseIds);

        Map<String, CorpusSliceConfig> configMap = corpusSliceConfigService.listByIds(configIds).stream()
                .collect(Collectors.toMap(CorpusSliceConfig::getId, config -> config, (a, b) -> a));

        // 转换实体为VO
        List<CorpusSliceTaskVO> taskVOs = tasks.getRecords().stream()
                .map(task -> {
                    CorpusSliceTaskVO vo = new CorpusSliceTaskVO(task);
                    vo.setCorpusName(corpusNameMap.getOrDefault(task.getCorpusId(), ""));
                    vo.setDatabasePath(databasePathMap.getOrDefault(task.getCorpusDatabaseId(), ""));
                    vo.setConfigInfo(configMap.get(task.getConfigId()).getName());
                    return vo;
                })
                .collect(Collectors.toList());

        // 返回结果
        return new Page<CorpusSliceTaskVO>(tasks.getCurrent(), tasks.getSize(), tasks.getTotal())
                .setRecords(taskVOs);
    }

    public Page<CorpusSliceDocument> queryDocuments(SliceQueryDocumentsDTO request) {
        Page<CorpusSliceDocument> page = new Page<>(request.getPageNo(), request.getPageSize());
        Map<String, String> tagMap =
                corpusTagsService.lambdaQuery().list().stream()
                        .collect(Collectors.toMap(c -> c.getId().toString(), CorpusTags::getName));
        Page<CorpusSliceDocument> documents;
        if (request.getQueryBody() == null) {
            documents =
                    corpusSliceDocumentService.lambdaQuery().orderByDesc(CorpusSliceDocument::getUpdateTime).page(page);
        } else {
            SliceQueryDocumentsDTO.DocumentQueryBody queryBody = request.getQueryBody();
            LambdaQueryWrapper<CorpusSliceDocument> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(queryBody.getId() != null, CorpusSliceDocument::getSliceTaskId, queryBody.getId());
            wrapper.eq(queryBody.getStatus() != null, CorpusSliceDocument::getStatus, queryBody.getStatus());
            wrapper.like(StringUtils.isNotBlank(queryBody.getTitle()), CorpusSliceDocument::getTitle, queryBody.getTitle());
            wrapper.eq(queryBody.getNumber() != null, CorpusSliceDocument::getNumber, queryBody.getNumber());
            wrapper.like(StringUtils.isNotBlank(queryBody.getImportMethod()), CorpusSliceDocument::getImportMethod, queryBody.getImportMethod());
            wrapper.between(queryBody.getCreateStartTime() != null && queryBody.getCreateEndTime() != null,
                    CorpusSliceDocument::getCreateTime, queryBody.getCreateStartTime(), queryBody.getCreateEndTime());
            wrapper.between(queryBody.getUpdateStartTime() != null && queryBody.getUpdateEndTime() != null,
                    CorpusSliceDocument::getUpdateTime, queryBody.getUpdateStartTime(), queryBody.getUpdateEndTime());
            List<String> documentIds =
                    corpusSliceDocumentService.list(wrapper).stream().map(CorpusSliceDocument::getId).collect(Collectors.toList());
            if (ArrayUtils.isNotEmpty(queryBody.getTags())) {
                for (String tag : queryBody.getTags()) {
                    documentIds.retainAll(corpusSliceDocumentService.lambdaQuery().apply("{0} = ANY(tags)", tag).list().stream().map(CorpusSliceDocument::getId).collect(Collectors.toList()));
                }
            }
            if (CollectionUtil.isNotEmpty(queryBody.getIds())) {
               documentIds=queryBody.getIds();
            }
            if (CollectionUtil.isEmpty(documentIds)) {
                documents = page;
            } else {
                documents = corpusSliceDocumentService.lambdaQuery().in(CorpusSliceDocument::getId, documentIds).page(page);
            }
        }
        documents.getRecords().forEach(record -> record.setTags(Arrays.stream(record.getTags()).map(tagMap::get).filter(Objects::nonNull).toArray(String[]::new)));
        return documents;
    }

    public Page<CorpusSliceSegment> querySegments(SliceQuerySegmentsDTO request) {
        Page<CorpusSliceSegment> page = new Page<>(request.getPageNo(), request.getPageSize());
        if (request.getQueryBody() == null) {
            return corpusSliceSegmentService.lambdaQuery().orderByDesc(CorpusSliceSegment::getUpdateTime).page(page);
        }
        SliceQuerySegmentsDTO.SegmentQueryBody queryBody = request.getQueryBody();
        LambdaQueryWrapper<CorpusSliceSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(queryBody.getId() != null, CorpusSliceSegment::getSliceTaskId, queryBody.getId());
        wrapper.eq(StringUtils.isNotBlank(queryBody.getDocumentId()), CorpusSliceSegment::getDocumentId, queryBody.getDocumentId());
        wrapper.in(CollectionUtil.isNotEmpty(queryBody.getDocumentIds()), CorpusSliceSegment::getDocumentId, queryBody.getDocumentIds());
        
        // 添加自定义SQL文本条件示
        if (request.getQueryBody().getTags() != null && !request.getQueryBody().getTags().isEmpty()) {
            // 使用 apply 拼接原生 SQL
            String safeTags = request.getQueryBody().getTags() .stream()
                    .filter(t -> t != null && t.matches("^-?\\d+$"))
                    .map(t -> "'" + t + "'")
                    .collect(Collectors.joining(","));

            if (!safeTags.isEmpty()) {
                wrapper.apply("tags && ARRAY["+safeTags+"]::text[]");
            }
        }
        wrapper.orderByDesc(CorpusSliceSegment::getUpdateTime);
        return corpusSliceSegmentService.page(page, wrapper);
    }

    public Map<String, List<String>> importDocuments(SliceImportDocumentsDTO request) {
        CorpusSliceTask task = corpusSliceTaskService.getById(request.getSliceTaskId());
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        List<String> ids = new ArrayList<>();
        for (String filePath : request.getFileList()) {
            CorpusSliceDocument document = new CorpusSliceDocument();
            document.setFilePath(filePath);
            document.setTitle(Arrays.stream(filePath.split("/")).reduce((a, b) -> b).orElse(filePath));
            document.setImportMethod("本地上传");
            document.setSliceTaskId(task.getId());
            corpusSliceDocumentService.save(document);
            ids.add(document.getId());
        }
        Map<String, List<String>> result = new HashMap<>();
        result.put("ids", ids);
        return result;
    }

    public Map<String, String> delete(Long id) {
        corpusSliceSegmentService.remove(new LambdaQueryWrapper<CorpusSliceSegment>().eq(CorpusSliceSegment::getSliceTaskId, id));
        corpusSliceDocumentService.remove(new LambdaQueryWrapper<CorpusSliceDocument>().eq(CorpusSliceDocument::getSliceTaskId, id));
        corpusSliceTaskService.removeById(id);
        Map<String, String> result = new HashMap<>();
        result.put("id", id.toString());
        return result;
    }

    public Map<String, String> deleteDocument(String id) {
        corpusSliceSegmentService.remove(new LambdaQueryWrapper<CorpusSliceSegment>().eq(CorpusSliceSegment::getDocumentId, id));
        corpusSliceDocumentService.removeById(id);
        Map<String, String> result = new HashMap<>();
        result.put("id", id);
        return result;
    }
}
