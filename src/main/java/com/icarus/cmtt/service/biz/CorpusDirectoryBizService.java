package com.icarus.cmtt.service.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.cmtt.dto.CorpusDirectoryDTO;
import com.icarus.cmtt.entity.CorpusCollectionConfig;
import com.icarus.cmtt.entity.CorpusDatabase;
import com.icarus.cmtt.service.ICorpusCollectionConfigService;
import com.icarus.cmtt.service.ICorpusDatabaseService;
import com.icarus.cmtt.vo.CorpusDatabaseVO;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CorpusDirectoryBizService {

    @Resource
    private ICorpusDatabaseService corpusDatabaseService;

    @Resource
    private ICorpusCollectionConfigService collectionConfigService;

    public Object queryCorpusDir() {
        // 获取目录信息
        List<CorpusDatabaseVO> directory = corpusDatabaseService.lambdaQuery()
                .eq(CorpusDatabase::getSubject, "FINANCIAL_COMMON")
                .list().stream().map(CorpusDatabaseVO::new)
                .collect(Collectors.toList());
        List<CorpusDatabaseTree> databaseTrees = new ArrayList<>();

        Map<String, CorpusDatabaseTree> map =
                directory.stream().map(CorpusDatabaseTree::new)
                        .collect(Collectors.toMap(CorpusDatabaseTree::getId, d -> d));

        directory.forEach(d -> {
            if (StringUtils.isBlank(d.getParentId())) {
                databaseTrees.add(map.get(d.getId()));
            } else {
                CorpusDatabaseTree parent = map.get(d.getParentId());
                if (parent == null) return;
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(map.get(d.getId()));
            }
        });
        Map<String, Object> result = new HashMap<>();
        result.put("dir", databaseTrees);
        // 返回JSON格式的成功响应
        return result;
    }

    public Object insertCorpusDir(CorpusDirectoryDTO properties) {
        // 创建新目录
        CorpusDatabase directory = new CorpusDatabase();

        if (StringUtils.isNotBlank(properties.getParentId())) {
            // 检查是否存在相同parentId的记录
            CorpusDatabase parent = corpusDatabaseService.getById(properties.getParentId());
            if (parent == null) {
                throw new RuntimeException("父目录id不存在");
            }
            directory.setParentId(parent.getId());
            directory.setType(parent.getType());
        } else {
            directory.setType(properties.getGroup());
        }

        if (StringUtils.isBlank(properties.getName())) {
            throw new RuntimeException("名称为空");
        }

        // 设置基础属性
        directory.setId(IdUtil.objectId());
        directory.setName(properties.getName());
        directory.setCreateTime(LocalDateTime.now());
        directory.setUpdateTime(LocalDateTime.now());
        directory.setSubject("FINANCIAL_COMMON");

        // 保存到数据库
        corpusDatabaseService.save(directory);

        return directory;
    }

    public Object updateCorpusDir(CorpusDirectoryDTO properties) {
        if (StringUtils.isBlank(properties.getId())) {
            throw new RuntimeException("目录ID不能为空");
        }
        // 获取现有目录
        CorpusDatabase existingDatabase = corpusDatabaseService.getById(properties.getId());

        if (existingDatabase == null) {
            throw new RuntimeException("目录不存在");
        }

        // 更新目录属性
        if (StringUtils.isNotBlank(properties.getParentId())) {
            // 检查是否存在相同parentId的记录
            CorpusDatabase parent = corpusDatabaseService.getById(properties.getParentId());
            if (parent == null) {
                throw new RuntimeException("父目录id不存在");
            }
            existingDatabase.setParentId(parent.getId());
            existingDatabase.setType(parent.getType());
        } else {
            existingDatabase.setParentId(null);
            existingDatabase.setType(properties.getGroup());
        }
        if (StringUtils.isNotBlank(properties.getName())) {
            existingDatabase.setName(properties.getName());
        }
        existingDatabase.setUpdateTime(LocalDateTime.now());

        //修改收集器中已有的目录
        //获取原全路径
        String fullPath = corpusDatabaseService.getFullPath(properties.getId());
        String replacePath = fullPath.replace(existingDatabase.getName(), properties.getName());
        collectionConfigService.<CorpusCollectionConfig>lambdaUpdate()
                .set(CorpusCollectionConfig::getCorpusDatabasePath, replacePath)
                        .likeRight(CorpusCollectionConfig::getCorpusDatabasePath, fullPath);

        // 保存更新
        corpusDatabaseService.updateById(existingDatabase);


        // 返回JSON格式的成功响应
        return existingDatabase;
    }


    public Object deleteCorpusDir(CorpusDirectoryDTO properties) {
        if (StringUtils.isBlank(properties.getId())) {
            throw new RuntimeException("目录ID不能为空");
        }
        // 删除目录
        LambdaQueryWrapper<CorpusDatabase> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CorpusDatabase::getId, properties.getId());

        // 获取现有目录
        CorpusDatabase existingDatabase = corpusDatabaseService.getOne(wrapper);

        if (existingDatabase == null) {
            throw new RuntimeException("目录不存在");
        }
        List<CorpusDatabase> removedCorpusDirs = new ArrayList<>();
        removedCorpusDirs.add(existingDatabase);

        // 检查是否存在子目录
        LambdaQueryWrapper<CorpusDatabase> childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(CorpusDatabase::getParentId, properties.getId());

        Queue<CorpusDatabase> queue = new LinkedList<>(corpusDatabaseService.list(childWrapper));

        while (!queue.isEmpty()) {
            CorpusDatabase database = queue.poll();
            childWrapper.eq(CorpusDatabase::getParentId, database.getId());
            queue.addAll(corpusDatabaseService.list(childWrapper));
            removedCorpusDirs.add(database);
        }
        for (CorpusDatabase database : removedCorpusDirs) {
            corpusDatabaseService.removeById(database);
        }
        List<String> removedCorpusDirIds = removedCorpusDirs.stream().map(CorpusDatabase::getId).collect(Collectors.toList());
        Map<String, Object> result = new HashMap<>();
        result.put("dir", removedCorpusDirIds);
        return result;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class CorpusDatabaseTree extends CorpusDatabaseVO {
        private List<CorpusDatabaseTree> children;

        public CorpusDatabaseTree(CorpusDatabaseVO corpusDatabase) {
            BeanUtils.copyProperties(corpusDatabase, this);
        }
    }
}
