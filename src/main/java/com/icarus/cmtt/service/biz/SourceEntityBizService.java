package com.icarus.cmtt.service.biz;

import cn.hutool.core.util.IdUtil;
import com.icarus.cmtt.dto.SourceEntityDTO;
import com.icarus.cmtt.entity.CorpusDictionary;
import com.icarus.cmtt.service.ICorpusDictionaryService;
import com.icarus.cmtt.vo.SourceEntityVO;
import jakarta.annotation.Resource;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SourceEntityBizService {
    @Resource
    private ICorpusDictionaryService corpusDictionaryService;

    public Object getEntity() {
        List<SourceEntityVO> entities =
                corpusDictionaryService.lambdaQuery().in(CorpusDictionary::getDictType, Arrays.asList("1", "2")).list().stream().map(SourceEntityVO::new).collect(Collectors.toList());
        Map<String, SourceEntityTree> map = entities.stream().map(SourceEntityTree::new
        ).collect(Collectors.toMap(SourceEntityVO::getId,
                Function.identity()));
        List<SourceEntityTree> entityTrees = new ArrayList<>();
        for (SourceEntityVO entity : entities) {
            if (StringUtils.isBlank(entity.getParentId())) {
                entityTrees.add(map.get(entity.getId()));
            } else {
                SourceEntityTree parent = map.get(entity.getParentId());
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(map.get(entity.getId()));
            }
        }
        return entityTrees;
    }

    public Object insertEntity(SourceEntityDTO request) {
        CorpusDictionary entity = new CorpusDictionary();
        entity.setId(IdUtil.objectId());
        entity.setDictValue(request.getName());
        entity.setParentId(request.getParentId());
        if (StringUtils.isBlank(entity.getParentId())) {
            entity.setDictType("1");
        } else {
            CorpusDictionary parent = corpusDictionaryService.getById(entity.getParentId());
            if (parent == null) {
                throw new RuntimeException("父id不存在");
            }
            entity.setDictType("2");
        }
        entity.setSourceInstitutionUrl(request.getSourceInstitutionUrl());
        entity.setDescription(request.getDescription());
        corpusDictionaryService.save(entity);
        Map<String, Object> result = new HashMap<>();
        result.put("id", entity.getId());
        return result;
    }

    public Object updateEntity(SourceEntityDTO request) {
        if (StringUtils.isBlank(request.getId())) {
            throw new RuntimeException("id不能为空");
        }
        CorpusDictionary entity = corpusDictionaryService.getById(request.getId());
        if (entity == null) {
            throw new RuntimeException("id不存在");
        }
        entity.setDictValue(request.getName());
        entity.setParentId(request.getParentId());
        if (StringUtils.isBlank(entity.getParentId())) {
            entity.setDictType("1");
        } else {
            CorpusDictionary parent = corpusDictionaryService.getById(entity.getParentId());
            if (parent == null){
                throw new RuntimeException("父id不存在");
            }
            entity.setDictType("2");
        }
        entity.setSourceInstitutionUrl(request.getSourceInstitutionUrl());
        entity.setDescription(request.getDescription());
        corpusDictionaryService.updateById(entity);
        Map<String, Object> result = new HashMap<>();
        result.put("id", entity.getId());
        return result;
    }

    public Object deleteEntity(SourceEntityDTO request) {
        if (StringUtils.isBlank(request.getId())) {
            throw new RuntimeException("id不能为空");
        }
        CorpusDictionary entity = corpusDictionaryService.getById(request.getId());
        if (entity == null) {
            throw new RuntimeException("id不存在");
        }
        List<String> ids = new ArrayList<>();
        ids.add(entity.getId());
        Queue<String> queue = new LinkedList<>();
        queue.add(entity.getId());
        while (!queue.isEmpty()) {
            List<String> childIds =
                    corpusDictionaryService.lambdaQuery()
                            .eq(CorpusDictionary::getParentId, queue.poll())
                            .list().stream().map(CorpusDictionary::getId)
                            .collect(Collectors.toList());
            ids.addAll(childIds);
            queue.addAll(childIds);
        }
        corpusDictionaryService.removeByIds(ids);
        Map<String, List<String>> result = new HashMap<>();
        result.put("ids", ids);
        return result;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    private static class SourceEntityTree extends SourceEntityVO {
        private List<SourceEntityVO> children;

        public SourceEntityTree(SourceEntityVO sourceEntity) {
            BeanUtils.copyProperties(sourceEntity, this);
        }
    }
}
