package com.icarus.cmtt.service.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.dto.corpus.sliceconfig.ConfigCreateDTO;
import com.icarus.cmtt.dto.corpus.sliceconfig.ConfigQueryDTO;
import com.icarus.cmtt.dto.corpus.sliceconfig.ConfigUpdateDTO;
import com.icarus.cmtt.entity.CorpusSliceConfig;
import com.icarus.cmtt.service.ICorpusSliceConfigService;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
public class CorpusSliceConfigBizService {
    @Resource
    private ICorpusSliceConfigService corpusSliceConfigService;

    public Object querySliceConfig(ConfigQueryDTO request) {
        Page<CorpusSliceConfig> page = new Page<>(request.getPageNo(), request.getPageSize());
        if (request.getQueryBody() == null) {
            return corpusSliceConfigService.lambdaQuery().orderByDesc(CorpusSliceConfig::getStatus).page(page);
        }
        ConfigQueryDTO.SliceConfigQueryBody queryBody = request.getQueryBody();
        LambdaQueryWrapper<CorpusSliceConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(queryBody.getId()), CorpusSliceConfig::getId, queryBody.getId());
        wrapper.like(StringUtils.isNotBlank(queryBody.getName()), CorpusSliceConfig::getName, queryBody.getName());
        wrapper.eq(queryBody.getCotId() != null, CorpusSliceConfig::getCotId, queryBody.getCotId());
        wrapper.eq(queryBody.getStatus() != null, CorpusSliceConfig::getStatus, queryBody.getStatus());
        wrapper.like(StringUtils.isNotBlank(queryBody.getDescription()), CorpusSliceConfig::getDescription, queryBody.getDescription());
        wrapper.between(queryBody.getCreateStartTime() != null && queryBody.getCreateEndTime() != null,
                CorpusSliceConfig::getCreateTime, queryBody.getCreateStartTime(), queryBody.getCreateEndTime());
        wrapper.between(queryBody.getUpdateStartTime() != null && queryBody.getUpdateEndTime() != null,
                CorpusSliceConfig::getUpdateTime, queryBody.getUpdateStartTime(), queryBody.getUpdateEndTime());
        wrapper.orderByDesc(CorpusSliceConfig::getStatus).orderByDesc(CorpusSliceConfig::getUpdateTime);
        return corpusSliceConfigService.page(page, wrapper);
    }

    public Object updateSliceConfig(ConfigUpdateDTO request) {
        LambdaUpdateWrapper<CorpusSliceConfig> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CorpusSliceConfig::getId, request.getId());
        wrapper.set(StringUtils.isNotBlank(request.getName()), CorpusSliceConfig::getName, request.getName())
                .set(StringUtils.isNotBlank(request.getCotId()), CorpusSliceConfig::getCotId, request.getCotId())
                .set(request.getSliceSize() != null, CorpusSliceConfig::getSliceSize, request.getSliceSize())
                .set(request.getOverlapSize() != null, CorpusSliceConfig::getOverlapSize, request.getOverlapSize());
        if (request.getStatus() == null || request.getStatus() == 0) {
            wrapper.set(CorpusSliceConfig::getStatus, 0);
        } else {
            CorpusSliceConfig config =
                    corpusSliceConfigService.lambdaQuery().eq(CorpusSliceConfig::getStatus, 1).one();
            if (config != null) {
                config.setStatus(0);
                config.setUpdateTime(LocalDateTime.now());
                corpusSliceConfigService.save(config);
            }
            wrapper.set(CorpusSliceConfig::getStatus, 1);
        }
        wrapper.set(CorpusSliceConfig::getUpdateTime, LocalDateTime.now());
        corpusSliceConfigService.update(wrapper);
        Map<String, String> map = new HashMap<>();
        map.put("id", request.getId());
        return map;
    }

    public Object createSliceConfig(ConfigCreateDTO request) {
        CorpusSliceConfig config = new CorpusSliceConfig();
        config.setId(IdUtil.objectId());
        config.setName(request.getName());
        config.setCotId(request.getCotId());
        config.setStatus(0);
        config.setSliceSize(request.getSliceSize());
        config.setOverlapSize(request.getOverlapSize());
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        corpusSliceConfigService.save(config);
        return config;
    }

    public Object deleteSliceConfig(String id) {
        corpusSliceConfigService.removeById(id);
        Map<String, String> result = new HashMap<>();
        result.put("id", id);
        return result;
    }
}
