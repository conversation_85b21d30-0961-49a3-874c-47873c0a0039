package com.icarus.cmtt.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.dto.CorpusSearchDTO;
import com.icarus.cmtt.dto.DifyDocumentDTO;
import com.icarus.cmtt.dto.DifyFilesDTO;
import com.icarus.cmtt.entity.*;
import com.icarus.cmtt.enums.OperationType;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.util.DifyUtil;
import io.swagger.annotations.ApiModelProperty;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 推送服务业务实现类
 */
@Slf4j
@Service
public class PushTaskBizService {

    private static  final  String CORPUS = "语料库";
    private static  final  String DATASETS = "知识库";
    private static final String DIFY = "Dify";
    private static final String API = "API";
    private static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2;
    private static final long KEEP_ALIVE_TIME = 60L;

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Resource
    private IPushTaskService pushTaskService;

    @Resource
    private ICorpusDatabaseService corpusDatabaseService;

    @Resource
    private IKnowledgeBaseSystemService knowledgeBaseSystemService;

    @Resource
    private ICorpusScopeRuleService corpusScopeRuleService;

    @Resource
    private CorpusSearchBizService searchBizService;

    @Resource
    private ICorpusDetailService corpusDetailService;

    @Resource
    private ICorpusFileService fileService;

    @Resource
    private ICorpusSliceSegmentService segmentService;

    @Resource
    private IKnowledgeBaseSystemService baseSystemService;

    @Resource
    private IKnowledgeTaskService knowledgeTaskService;




    public Object handle(JSONObject jsonObject) {
        //解析固定对象

        OperationType operationType = OperationType.valueOf(jsonObject.getStr("operationType"));

        String bodyJson = jsonObject.getStr("bodyJson");
        switch (operationType) {
            case UPDATE:
                // 更新操作：将JSON转换为实体对象，并调用update方法
                return this.rePush(JSONUtil.toBean(bodyJson, RePushDTO.class, true));
            case QUERY:
                // 查询操作：将JSON转换为查询请求对象，并调用page方法
                return this.pageQuery(JSONUtil.toBean(bodyJson, PageReq.class, true));
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operationType);
        }
    }

    private Page<PushTask> pageQuery(PageReq pageReq) {
        Page<PushTask> page = new Page<>(pageReq.getPageNo(), pageReq.getPageSize());
        QueryBody queryBody = pageReq.getQueryBody();
        LambdaQueryWrapper<PushTask> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(PushTask::getCreateTime);
        //条件查询
        if (queryBody != null) {
            wrapper.eq(queryBody.getStatus() != null, PushTask::getStatus, queryBody.getStatus());
            wrapper.like(StrUtil.isNotBlank(queryBody.getCorpusDictionary()), PushTask::getCorpusDictionary, queryBody.getCorpusDictionary());
            wrapper.like(StrUtil.isNotBlank(queryBody.getCorpusName()), PushTask::getCorpusName, queryBody.getCorpusName());
            wrapper.like(StrUtil.isNotBlank(queryBody.getId()), PushTask::getId, queryBody.getId());
            if (CollectionUtil.isNotEmpty(queryBody.getCreateTime())) {
                wrapper.gt(StrUtil.isNotBlank(queryBody.getCreateTime().get(0)),
                        PushTask::getCreateTime, convertStringToLocalDateTime(queryBody.getCreateTime().get(0)));
                wrapper.lt(StrUtil.isNotBlank(queryBody.getCreateTime().get(1)),
                        PushTask::getCreateTime, convertStringToLocalDateTime(queryBody.getCreateTime().get(1)));
            }
            if (CollectionUtil.isNotEmpty(queryBody.getBeginTime())) {
                wrapper.gt(StrUtil.isNotBlank(queryBody.getBeginTime().get(0)),
                        PushTask::getBeginTime, convertStringToLocalDateTime(queryBody.getBeginTime().get(0)));
                wrapper.lt(StrUtil.isNotBlank(queryBody.getBeginTime().get(1)),
                        PushTask::getBeginTime, convertStringToLocalDateTime(queryBody.getBeginTime().get(1)));
            }
            if (CollectionUtil.isNotEmpty(queryBody.getEndTime())) {
                wrapper.gt(StrUtil.isNotBlank(queryBody.getEndTime().get(0)),
                        PushTask::getEndTime, convertStringToLocalDateTime(queryBody.getEndTime().get(0)));
                wrapper.lt(StrUtil.isNotBlank(queryBody.getEndTime().get(1)),
                        PushTask::getEndTime, convertStringToLocalDateTime(queryBody.getEndTime().get(1)));
            }
        }
        return pushTaskService.page(page, wrapper);
    }


    public Boolean rePush(RePushDTO rePushDTO) {
        //单个任务重新处理
        if (StrUtil.isBlank(rePushDTO.getDimension())){
            log.info("单个任务重新处理");
            PushTask pushTask = pushTaskService.getById(rePushDTO.getTaskId());
            if (pushTask == null){
                throw new RuntimeException("重新处理任务不存在");
            }
            if (pushTask.getStatus() != 2 && pushTask.getStatus() != 3){
                throw new RuntimeException("任务未完成");
            }
            pushTask.setStatus(0);
            return pushTaskService.updateById(pushTask);

        }else{
            //批量处理 语料库
            if (CORPUS.equals(rePushDTO.dimension)){
                // 异步执行
                log.info("异步执行语料库重新推送");
                CompletableFuture.runAsync(() -> {
                    changeStatusFromDictionary(rePushDTO.getNodeIds());
                });
            }
            //批量处理 知识库
            if (DATASETS.equals(rePushDTO.dimension)){
                log.info("异步执行知识库重新推送");
                CompletableFuture.runAsync(() -> {
                    changeStatusFromDatasets(rePushDTO.getNodeIds());
                });
            }
        return  true;
        }
    }


    public void changeStatusFromDictionary(List<String> nodeIds) {
        //修改任务状态
        Set<String> result = new HashSet<>();
        for (String id : nodeIds) {
            List<String> nodeId = corpusDatabaseService.getNodeId(id);
            result.addAll(nodeId);
        }
        if (CollectionUtil.isNotEmpty(result)) {
            List<String> details = corpusDetailService.<CorpusDetail>lambdaQuery().select(CorpusDetail::getId)
                    .in(CorpusDetail::getCorpusDatabaseId, result).list()
                    .stream().map(CorpusDetail::getId).collect(Collectors.toList());
            result.addAll(details);
            if(CollectionUtil.isNotEmpty(details)){
                log.info("语料库批量处理 修改任务状态,参数:{}",details);
                pushTaskService.<PushTask>lambdaUpdate().
                        in(PushTask::getStatus,2,3).in(PushTask::getCorpusId, result).set(PushTask::getStatus, 0).update();
            }
        }
    }

    public void changeStatusFromDatasets(List<String> nodeIds) {
        Set<String> result = new HashSet<>();
        for (String nodeId : nodeIds) {
            //获取当前目录下所有知识库
            List<String> nodes = knowledgeBaseSystemService.getNodes(nodeId);
            if (CollectionUtil.isNotEmpty(nodes)) {
                    result.addAll(getDetailIdFromSystem(nodes));
                }
            }
        if (CollectionUtil.isNotEmpty(result)){
            log.info("知识库批量处理 修改任务状态,参数:{}",result);
            pushTaskService.<PushTask>lambdaUpdate().in(PushTask::getStatus,2,3).
                    in(PushTask::getCorpusId, result).set(PushTask::getStatus, 0).update();
        }
    }


    public Set<String> getDetailIdFromSystem(List<String> nodes){
        List<CorpusScopeRule> list = corpusScopeRuleService.<CorpusScopeRule>lambdaQuery().in(CorpusScopeRule::getSystemId, nodes).list();
        if (CollectionUtil.isNotEmpty(list)) {
            //将CorpusScopeRule对象转化成标签处理对象
            CorpusSearchDTO properties = new CorpusSearchDTO();
            ArrayList<CorpusSearchDTO.BusinessTag> businessTags = new ArrayList<>();
            //循环处理
            for (CorpusScopeRule corpusScopeRule : list) {
                if (StrUtil.isNotBlank(corpusScopeRule.getValue())) {
                    String[] tags = corpusScopeRule.getValue().split(",");
                    for (String tag : tags) {
                        CorpusSearchDTO.BusinessTag businessTag = new CorpusSearchDTO.BusinessTag();
                        businessTag.setCondition(CorpusSearchDTO.Condition.parse(corpusScopeRule.getOperator()));
                        businessTag.setMatchMode(CorpusSearchDTO.MatchMode.parse(corpusScopeRule.getLogicWithPrev()));
                        businessTag.setValue(tag);
                        businessTags.add(businessTag);
                    }
                }
            }
            properties.setBusinessTags(businessTags);
            //获取所有语料详情id
            return searchBizService.matchTag(properties);
        }else {
            return new HashSet<String>();
        }
    }
    private static LocalDateTime convertStringToLocalDateTime(String dateStr) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析字符串并转换为 LocalDateTime (默认时间为00:00:00)
        return LocalDateTime.parse(dateStr, formatter);
    }

    public void push() {
        //获取所有待处理的任务
        List<PushTask> list = pushTaskService.lambdaQuery().eq(PushTask::getStatus, 0).list();
        processBatchTasks(list);
    }

    // 批量任务处理方法
    public void processBatchTasks(List<PushTask> tasks) {
        for (PushTask task : tasks) {
            try {
                log.info("开始推送任务，任务ID: {}", task.getId());
                task.setStatus(1);
                task.setBeginTime(LocalDateTime.now());
                pushTaskService.updateById(task);
                // 实际推送逻辑
                performPushOperation(task);
                task.setStatus(2);
                task.setEndTime(LocalDateTime.now());
                pushTaskService.updateById(task);
                log.info("推送任务完成，任务ID: {}", task.getId());
            } catch (Exception e) {
                log.error("推送任务失败，任务ID: {}, 错误信息: {}", task.getId(), e.getMessage());
                task.setStatus(3);
                task.setEndTime(LocalDateTime.now());
                pushTaskService.updateById(task);
            }
        }
    }
    // 实际推送操作方法
    public void performPushOperation(PushTask task) throws Exception {
        // 获取语料可能关联的知识库
        List<KnowledgeBaseSystem> fromCorpus = getFromCorpus(task.getCorpusId());
        //获取所有语料文件
        List<CorpusFile> corpusFiles = fileService.<CorpusFile>lambdaQuery()
                .select(CorpusFile::getId, CorpusFile::getName)
                .eq(CorpusFile::getCorpusId, task.getCorpusId())
                .list();
        Map<String, String> hashMap = corpusFiles.stream()
                .collect(Collectors.toMap(CorpusFile::getId, CorpusFile::getName, (existing, replacement) -> existing));

        //获取所有的切片
        List<CorpusSliceSegment> segments = segmentService.lambdaQuery()
                .select(CorpusSliceSegment::getId,CorpusSliceSegment::getFileId, CorpusSliceSegment::getIndex, CorpusSliceSegment::getContent)
            .in(CorpusSliceSegment::getFileId, hashMap.keySet())
            .orderByAsc(CorpusSliceSegment::getIndex)
            .list();

        // 按照file_id进行分组
        Map<String, List<CorpusSliceSegment>> finalSegment = segments.stream()
            .collect(Collectors.groupingBy(CorpusSliceSegment::getFileId));
        log.info("收集完成任务需要处理的知识库和分片，任务ID: {}", task.getId());
        // 串行处理每个知识库和对应的分片
        if (fromCorpus!= null) {
            fromCorpus.forEach(knowledgeBase -> {
                processKnowledgeContent(task.getId(), knowledgeBase, hashMap, finalSegment);
            });
        }
    }


    // 使用自定义线程池来并行处理知识库内容

    private void processKnowledgeContent(String taskId,KnowledgeBaseSystem knowledgeBase,Map<String,String> map,Map<String, List<CorpusSliceSegment>> finalSegment) {
        EXECUTOR.submit(() -> {
            try {
                //前置清空
                preProcess(taskId,knowledgeBase);
                //重新推送
                List<DifyDocumentDTO> difyDocumentDTOS = new ArrayList<>();
                // 遍历每个文件的分片数据并执行处理逻辑
                for (Map.Entry<String, List<CorpusSliceSegment>> entry : finalSegment.entrySet()) {
                    List<CorpusSliceSegment> segments = entry.getValue();
                    // 模拟推送处理逻辑，如上传到知识库等
                    for (CorpusSliceSegment segment : segments) {
                        // 执行每个分片的推送操作
                        log.info("推送分片信息: {}", segment.getId());
                        pushContent2KnowledgeBase(difyDocumentDTOS,knowledgeBase,map.get(entry.getKey()), segment);
                    }
                }
                //执行后续Dify文件回填
                DifyFilesDTO difyFilesDTO = new DifyFilesDTO();
                difyFilesDTO.setDifyDocumentDTOS(difyDocumentDTOS);
                difyFilesDTO.setId(knowledgeBase.getDifyId());
                baseSystemService.saveFiles(difyFilesDTO);
                //保存关联关系 用于重启任务时刷数据
                knowledgeTaskService.saveRelation(taskId,difyFilesDTO);
                log.info("推送到知识库{}成功", knowledgeBase.getDifyId());
            } catch (Exception e) {
                log.error("推送到知识库{}失败:, 错误信息: {}", knowledgeBase.getId(), e.getMessage());
            }
        });
    }

    private void preProcess(String taskId,KnowledgeBaseSystem knowledgeBaseSystem) {
        log.info("开始清理知识库{}", knowledgeBaseSystem.getId());
        //暂时只支持dify api形式
        if (DIFY.equals(knowledgeBaseSystem.getAssociatedSystem())) {
            if (API.equals(knowledgeBaseSystem.getConnectMode())) {
                String baseUrl = knowledgeBaseSystem.getApi();
                String apiKey = knowledgeBaseSystem.getApiKey().replace("Bearer ", "");
                List<KnowledgeTask> fileList = knowledgeTaskService.lambdaQuery()
                        .eq(KnowledgeTask::getKnowledgeId, knowledgeBaseSystem.getDifyId()).eq(KnowledgeTask::getTaskId, taskId)
                        .list();
                ArrayList<KnowledgeTask> finishDel = new ArrayList<>();
                for (KnowledgeTask file : fileList) {
                    try {
                        DifyUtil.deleteDocument(baseUrl, knowledgeBaseSystem.getDifyId(),file.getFileId(),apiKey);
                        //需要删除的关联关系
                        finishDel.add(file);
                    } catch (Exception e) {
                        log.error("删除{}DIFY文档失败:{}", file.getFileId(),e.getMessage());
                    }

                }
                //移除关联关系
                knowledgeTaskService.removeBatchByIds(finishDel);
            }
        }
    }

    public void pushContent2KnowledgeBase(List<DifyDocumentDTO> difyDocumentDTOS,KnowledgeBaseSystem knowledgeBase,String fileName, CorpusSliceSegment segment){
        //暂时只支持dify api形式
        if (DIFY.equals(knowledgeBase.getAssociatedSystem())) {
            if (API.equals(knowledgeBase.getConnectMode())) {
                String baseUrl = knowledgeBase.getApi();
                String apiKey = knowledgeBase.getApiKey().replace("Bearer ", "");
                String result = DifyUtil.createDocumentByText(baseUrl, knowledgeBase.getDifyId(), apiKey, fileName + "_" + segment.getIndex(), segment.getContent());
                JSONObject jsonObject = JSONUtil.parseObj(result);
                JSONObject document = (JSONObject) jsonObject.getObj("document");
                if (document == null) {
                    log.error("创建DIFY文档失败:{}", result);
                }
                try {
                    DifyDocumentDTO bean = JSONUtil.toBean(document, DifyDocumentDTO.class);
                    difyDocumentDTOS.add(bean);
                } catch (Exception e) {
                    log.error("转化DIFY文档DTO失败:{}", e.getMessage());
                }
            }
        }
    }


    /**
     * 获取语料关联知识库
     */
    public List<KnowledgeBaseSystem> getFromCorpus(String corpusId){
        CorpusDetail corpusDetail = corpusDetailService.lambdaQuery()
                .select(CorpusDetail::getTags)
                .eq(CorpusDetail::getId, corpusId)
                .one();
        if (corpusDetail == null || CollectionUtil.isEmpty(Arrays.asList(corpusDetail.getTags()))) {
            return null;
        }
        List<String> systemIdFromTags = corpusScopeRuleService.getSystemIdFromTags(corpusDetail.getTags());
        if (CollectionUtil.isEmpty(systemIdFromTags)){
            return null;
        }
        return knowledgeBaseSystemService.lambdaQuery()
                .in(KnowledgeBaseSystem::getId, systemIdFromTags)
                .list();
    }

    /**
     * 构建新推送任务
     * @param corpusId 语料id
     * @return 构建是否成功
     */
    public Boolean createPushTask(String corpusId) {
        //
        CorpusDetail corpusDetail = corpusDetailService.lambdaQuery().select(CorpusDetail::getCorpusDatabaseId,CorpusDetail::getName).eq(CorpusDetail::getId, corpusId).one();
        if (corpusDetail==null){
            log.info("语料不存在或目录信息错误");
        }
        PushTask pushTask = new PushTask();
        pushTask.setId(IdUtil.objectId());
        pushTask.setCorpusId(corpusId);
        pushTask.setCorpusDictionary(corpusDatabaseService.getFullPath(corpusDetail.getCorpusDatabaseId()));
        pushTask.setCorpusName(corpusDetail.getName());
        pushTask.setCreateTime(LocalDateTime.now());
        //判断是否关联知识库
        List<KnowledgeBaseSystem> fromCorpus = getFromCorpus(corpusId);
        pushTask.setStatus(CollectionUtil.isEmpty(fromCorpus)?2:0);
        return pushTaskService.save(pushTask);
    }




    @Data
    public static class RePushDTO {
        @ApiModelProperty("任务Id")
        private String taskId;
        @ApiModelProperty("处理维度")
        private String dimension;
        @ApiModelProperty("目录Ids")
        private List<String> nodeIds;
    }

    @Data
    public static class PageReq {
        private Integer pageNo;
        private Integer pageSize;
        private QueryBody queryBody;
    }

    @Data
    public static class QueryBody {
        private Integer status;
        private String id;
        private String corpusDictionary;
        private String corpusName;
        private List<String> beginTime;
        private List<String> endTime;
        private List<String> createTime;
    }
}