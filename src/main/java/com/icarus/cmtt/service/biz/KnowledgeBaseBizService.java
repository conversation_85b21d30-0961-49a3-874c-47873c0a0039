package com.icarus.cmtt.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.cmtt.dto.corpus.slice.SliceQueryDocumentsDTO;
import com.icarus.cmtt.dto.corpus.slice.SliceQuerySegmentsDTO;
import com.icarus.cmtt.entity.*;
import com.icarus.cmtt.enums.OperationType;
import com.icarus.cmtt.service.*;
import com.icarus.cmtt.vo.KnowledgeBaseManageListVO;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 知识库管理实现类
 */
@Slf4j
@Service
public class KnowledgeBaseBizService {

    @Resource
    IKnowledgeBaseSystemService baseSystemService;



    @Resource
    ICorpusSliceDocumentService corpusSliceDocumentService;

    @Resource
    ICorpusSliceSegmentService corpusSliceSegmentService;

    @Resource
    ICorpusScopeRuleService corpusScopeRuleService;

    @Resource
    CorpusSliceTaskBizService corpusSliceTaskBizService;


    @Resource
    ICorpusTagsService corpusTagsService;



    private  final static String corpusfile = "corpusfile";
    private  final static String knowledgefile = "knowledgefile";


    public Object handle(JSONObject jsonObject) {
        //解析固定对象

        OperationType operationType = OperationType.valueOf(jsonObject.getStr("operationType"));

        String bodyJson = jsonObject.getStr("bodyJson");
        switch (operationType) {
            case LIST:
                //查询切片
                return this.pageQuerySegment(JSONUtil.toBean(bodyJson, SliceQuerySegmentsDTO.class, true));
            case QUERY_CHILD:
                // 查询目录下的文件
                return this.pageQueryDocument(JSONUtil.toBean(bodyJson, SliceQueryDocumentsDTO.class, true));
            case QUERY:
                // 查询操作：将JSON转换为查询请求对象，并调用page方法
                return this.pageQuery(JSONUtil.toBean(bodyJson, PageReq.class, true));
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operationType);
        }
    }

    private Object pageQuerySegment(SliceQuerySegmentsDTO dto) {
        String knowledgeBaseSystemId = dto.getQueryBody().getKnowledgeBaseSystemId();
        if (StrUtil.isBlank(knowledgeBaseSystemId)){
            throw new RuntimeException("知识库id不为空");
        }
        KnowledgeBaseSystem knowledgebase = baseSystemService.lambdaQuery().select(KnowledgeBaseSystem::getFileDimension)
                .eq(KnowledgeBaseSystem::getId,knowledgeBaseSystemId).one();
        //语料文件维度
        if (corpusfile.equals(knowledgebase.getFileDimension())) {
            //获取关联的文件
            List<String> fileIdsBySystemId = corpusSliceDocumentService.getFileIdsBySystemId(knowledgeBaseSystemId);
            if (CollectionUtil.isEmpty(fileIdsBySystemId)) {
                return null;
            }
            SliceQuerySegmentsDTO.SegmentQueryBody queryBody = dto.getQueryBody();
            queryBody.setDocumentIds(fileIdsBySystemId);
            return corpusSliceTaskBizService.querySegments(dto);
        }
        if (knowledgefile.equals(knowledgebase.getFileDimension())){
            //获取指定文件下的标签 传了文件id 指定文件下的标签
            String documentId = dto.getQueryBody().getDocumentId();
            CorpusScopeRule byId = null;
            if (StrUtil.isNotBlank(documentId)) {
                 byId = corpusScopeRuleService.getById(documentId);
            }

            LambdaQueryWrapper<CorpusScopeRule> wrapper = new LambdaQueryWrapper<CorpusScopeRule>()
                    .select(CorpusScopeRule::getValue)
                    .eq(CorpusScopeRule::getSystemId, knowledgeBaseSystemId);

            // 只有 when byId != null 时才添加 group_id 条件
            if (byId != null) {
                wrapper.eq(CorpusScopeRule::getGroupId, byId.getGroupId());
            }
            List<CorpusScopeRule> list =corpusScopeRuleService.getBaseMapper().selectList( wrapper);

            List<String> tagIds = list.stream()
                    .filter(rule -> StrUtil.isNotBlank(rule.getValue()))
                    .flatMap(rule -> Arrays.stream(rule.getValue().split(",")))
                    .collect(Collectors.toList());
            dto.getQueryBody().setTags(tagIds);

            return corpusSliceTaskBizService.querySegments(dto);
        }
        return null;

    }

    private Object pageQueryDocument(SliceQueryDocumentsDTO dto) {
        String knowledgeBaseSystemId = dto.getQueryBody().getKnowledgeBaseSystemId();
        if (StrUtil.isBlank(knowledgeBaseSystemId)){
            throw new RuntimeException("知识库id不得为空");
        }
        //判断文件维度
        KnowledgeBaseSystem knowledgebase = baseSystemService.lambdaQuery().select(KnowledgeBaseSystem::getFileDimension)
                .eq(KnowledgeBaseSystem::getId,knowledgeBaseSystemId).one();
        if (corpusfile.equals(knowledgebase.getFileDimension())){
            SliceQueryDocumentsDTO.DocumentQueryBody queryBody = dto.getQueryBody();
            //获取当前知识库的标签
            List<String> tagIds = getTagIds(knowledgeBaseSystemId);
            String[] tags = queryBody.getTags();
            String[] finalTags;
            if (queryBody.getTags() == null){
                finalTags= tagIds.toArray(new String[0]);
            }else{
                finalTags = Arrays.stream(tags)
                        .filter(tagIds::contains)  // 只保留存在于tagIds中的元素
                        .toArray(String[]::new);
            }
            //需要对条件查询后的id强校验
            queryBody.setIds(corpusSliceDocumentService.getFileIdsBySystemId(knowledgeBaseSystemId));
            queryBody.setTags(finalTags);
            return corpusSliceTaskBizService.queryDocuments(dto);
        }
        if (knowledgefile.equals(knowledgebase.getFileDimension())) {
            //获取所有标签
            Map<String, String> tagMap =
                    corpusTagsService.lambdaQuery().list().stream()
                            .collect(Collectors.toMap(c -> c.getId().toString(), CorpusTags::getName));

            //拼装文件需要手动分页 CorpusSliceDocument
            //根据systemId查询所有不重复的name
            List<CorpusScopeRule> list = corpusScopeRuleService.lambdaQuery()
                    .eq(CorpusScopeRule::getSystemId, knowledgeBaseSystemId)
                    .isNotNull(CorpusScopeRule::getGroupId)
                    .orderByDesc(CorpusScopeRule::getCreatedTime)
                    .list();
            HashMap<String, CorpusScopeRule> nameMap = new HashMap<>();
            //清洗list
            for (CorpusScopeRule corpusScopeRule : list) {
                nameMap.merge(corpusScopeRule.getName(), corpusScopeRule, (existingRule, newRule) -> {
                    existingRule.setValue(existingRule.getValue() + "," + newRule.getValue());
                    return existingRule;
                });
            }
            List<CorpusScopeRule> collect = new ArrayList<>(nameMap.values());
            //获取每个文件下的切片数量
            List<CorpusSliceDocument> result = collect.parallelStream().map(corpusScopeRule -> {
                CorpusSliceDocument sliceDocument = new CorpusSliceDocument();
                //基础字段赋值
                sliceDocument.setId(corpusScopeRule.getId());
                sliceDocument.setImportMethod("语料库导入");
                String[] split = corpusScopeRule.getValue().split(",");
                //将split的值去重
                Set<String> uniqueTags = new HashSet<>(Arrays.asList(split));
                Integer count = corpusSliceSegmentService.getCountByTags(new ArrayList<>(uniqueTags));
                sliceDocument.setNumber(count);
                sliceDocument.setStatus(2);
                sliceDocument.setTitle(corpusScopeRule.getName());
                sliceDocument.setCreateTime(corpusScopeRule.getCreatedTime());
                sliceDocument.setUpdateTime(corpusScopeRule.getCreatedTime());
                List<String> tagIds = Arrays.asList(corpusScopeRule.getValue().split(","));
                sliceDocument.setTagIds(tagIds);
                //获取标签对应的名称
                sliceDocument.setTags(tagIds.stream()
                        .map(tagMap::get)
                        .filter(Objects::nonNull)
                        .distinct() // 去除重复元素
                        .toArray(String[]::new));
                return sliceDocument;
            }).collect(Collectors.toList());
            // 根据SliceQueryDocumentsDTO的字段result进行过滤，实现手动分页的效果
            List<CorpusSliceDocument> filteredResult = result.stream()
                    .filter(document -> {
                        SliceQueryDocumentsDTO.DocumentQueryBody queryBody = dto.getQueryBody();
                        if (queryBody == null)
                            return true;
                        // 标题过滤
                        if (StrUtil.isNotBlank(queryBody.getTitle()) &&
                                !document.getTitle().contains(queryBody.getTitle())) {
                            return false;
                        }
                        // 状态过滤
                        if (queryBody.getStatus() != null && !queryBody.getStatus().equals(document.getStatus())) {
                            return false;
                        }
                        // 切片数量过滤
                        if (queryBody.getNumber() != null && !queryBody.getNumber().equals(document.getNumber())) {
                            return false;
                        }
                        // 导入方式过滤
                        if (StrUtil.isNotBlank(queryBody.getImportMethod()) &&
                                !document.getImportMethod().equals(queryBody.getImportMethod())) {
                            return false;
                        }
                        // 创建时间范围过滤
                        if (queryBody.getCreateStartTime() != null && document.getCreateTime().isBefore(queryBody.getCreateStartTime())) {
                            return false;
                        }
                        if (queryBody.getCreateEndTime() != null && document.getCreateTime().isAfter(queryBody.getCreateEndTime())) {
                            return false;
                        }
                        // 更新时间范围过滤
                        if (queryBody.getUpdateStartTime() != null && document.getUpdateTime().isBefore(queryBody.getUpdateStartTime())) {
                            return false;
                        }
                        if (queryBody.getUpdateEndTime() != null && document.getUpdateTime().isAfter(queryBody.getUpdateEndTime())) {
                            return false;
                        }
                        // 标签过滤
                        if (queryBody.getTags() != null && queryBody.getTags().length > 0) {
                            return  document.getTagIds().containsAll(Arrays.asList(queryBody.getTags()));
                        }

                        return true;
                    })
                    .collect(Collectors.toList());

            // 手动分页
            int total = filteredResult.size();
            int pageSize = dto.getPageSize();
            int pageNo = dto.getPageNo();
            int fromIndex = (pageNo - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, total);

            List<CorpusSliceDocument> pagedResult = new ArrayList<>();
            if (fromIndex < total) {
                pagedResult = filteredResult.subList(fromIndex, toIndex);
            }

            // 构造返回的Page对象
            Page<CorpusSliceDocument> pageResult = new Page<>(pageNo, pageSize, total);
            pageResult.setRecords(pagedResult);

            return pageResult;
        }
        return null;

    }

    public Page<KnowledgeBaseManageListVO> pageQuery(PageReq req) {
        //获取所有标签
        Map<String, String> tagMap =
                corpusTagsService.lambdaQuery().list().stream()
                        .collect(Collectors.toMap(c -> c.getId().toString(), CorpusTags::getName));

        Page<KnowledgeBaseSystem> page = new Page<>(req.getPageNo(), req.getPageSize());
        QueryBody queryBody = req.getQueryBody();
        LambdaQueryWrapper<KnowledgeBaseSystem> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(KnowledgeBaseSystem::getCreateTime);
        //获取该目录下的所有id
        List<String> nodes = baseSystemService.getNodes(queryBody.getId());
        if (CollectionUtil.isEmpty(nodes)){
            //说明该目录下没有知识库
            return null;
        }
        wrapper.in(KnowledgeBaseSystem::getId,nodes);
        wrapper.like(StrUtil.isNotBlank(queryBody.getName()),KnowledgeBaseSystem::getName,queryBody.getName());
        if (CollectionUtil.isNotEmpty(queryBody.getCreateTime())) {
            wrapper.gt(StrUtil.isNotBlank(queryBody.getCreateTime().get(0)),
                    KnowledgeBaseSystem::getCreateTime, convertStringToLocalDateTime(queryBody.getCreateTime().get(0)));
            wrapper.lt( StrUtil.isNotBlank(queryBody.getCreateTime().get(1)),
                    KnowledgeBaseSystem::getCreateTime, convertStringToLocalDateTime(queryBody.getCreateTime().get(1)));
        }
        if (CollectionUtil.isNotEmpty(queryBody.getUpdateTime())) {
            wrapper.gt(StrUtil.isNotBlank(queryBody.getUpdateTime().get(0)),
                    KnowledgeBaseSystem::getUpdateTime, convertStringToLocalDateTime(queryBody.getUpdateTime().get(0)));
            wrapper.lt( StrUtil.isNotBlank(queryBody.getUpdateTime().get(1)),
                    KnowledgeBaseSystem::getUpdateTime, convertStringToLocalDateTime(queryBody.getUpdateTime().get(1)));
        }
        Page<KnowledgeBaseSystem> baseSystemPage = baseSystemService.page(page, wrapper);
        ArrayList<KnowledgeBaseManageListVO> records = new ArrayList<>();
        baseSystemPage.getRecords().parallelStream().forEach(record -> {
            KnowledgeBaseManageListVO vo = new KnowledgeBaseManageListVO();
            vo.setId(record.getId());
            vo.setName(record.getName());
            //处理文件数、字数，标签
            dealWithFilDimension(vo, record,tagMap);
            vo.setCreateTime(record.getCreateTime());
            vo.setUpdateTime(record.getUpdateTime());
            vo.setFileDimension(record.getFileDimension());
            records.add(vo);
        });
        Page<KnowledgeBaseManageListVO> resultPage = new Page<>(req.getPageNo(), req.getPageSize(), baseSystemPage.getTotal());
        resultPage.setRecords(records);
        return resultPage;
    }

    private List<String> getTagIds(String id) {
        List<CorpusScopeRule> list = corpusScopeRuleService.lambdaQuery().select(CorpusScopeRule::getValue).eq(CorpusScopeRule::getSystemId, id).list();
        HashSet<String> hashSet = new HashSet<>();
        for (CorpusScopeRule corpusScopeRule : list) {
            if (StrUtil.isNotBlank(corpusScopeRule.getValue())) {
                String[] tags = corpusScopeRule.getValue().split(",");
                hashSet.addAll(Arrays.asList(tags));
            }
        }
        return new ArrayList<>(hashSet);
    }

    //处理语料维度
    private void dealWithFilDimension(KnowledgeBaseManageListVO vo,KnowledgeBaseSystem baseSystem,Map<String,String> map){
        List<CorpusScopeRule> scopeRules = corpusScopeRuleService.lambdaQuery()
                .select(CorpusScopeRule::getValue)
                .eq(CorpusScopeRule::getSystemId, baseSystem.getId())
                .isNotNull(knowledgefile.equals(baseSystem.getFileDimension()),CorpusScopeRule::getGroupId)
                .list();

        List<String> tagNames = scopeRules.stream()
                .filter(rule -> StrUtil.isNotBlank(rule.getValue()))
                .flatMap(rule -> Arrays.stream(rule.getValue().split(",")))
                .map(map::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        vo.setTags(tagNames);
        //语料维度
        if (corpusfile.equals(baseSystem.getFileDimension())){
           List<String> fileIds = corpusSliceDocumentService.getFileIdsBySystemId(baseSystem.getId());
            if (CollectionUtil.isEmpty(fileIds)){
                vo.setFileCount(0);
                vo.setCharCount("0");
            }else {
                vo.setFileCount(fileIds.size());
                //获取
                int charCount = corpusSliceSegmentService.lambdaQuery()
                        .select(CorpusSliceSegment::getLength)
                        .in(CorpusSliceSegment::getDocumentId, fileIds).list().stream().mapToInt(CorpusSliceSegment::getLength).sum();
                vo.setCharCount(charCount/1000D+"K");
            }
        }
        //自定义
        if (knowledgefile.equals(baseSystem.getFileDimension())){
            List<CorpusScopeRule> list = corpusScopeRuleService.lambdaQuery().select(CorpusScopeRule::getGroupOrder,CorpusScopeRule::getValue).
                    eq(CorpusScopeRule::getSystemId, baseSystem.getId()).
                    isNotNull(CorpusScopeRule::getOrder).orderByDesc(CorpusScopeRule::getGroupOrder).list();
            vo.setFileCount(list.isEmpty() ? 0 : Integer.parseInt(list.get(0).getGroupOrder()));
            //获取所有的切片标签
            if (list.isEmpty()){
                //统计字数
                vo.setCharCount("0K");
            }else {
                //获取所有标签
                Set<String> tagIds = new HashSet<>();
                for (CorpusScopeRule corpusScopeRule : list) {
                    String[] split = corpusScopeRule.getValue().split(",");
                    tagIds.addAll(Arrays.asList(split));
                }
                //查询所有切片
                Integer length = corpusSliceSegmentService.getSumLengthByTags(new ArrayList<>(tagIds));
                if (length != null){
                    vo.setCharCount(length/1000D+"K");
                }
            }
        }
    }


    private static LocalDateTime convertStringToLocalDateTime(String dateStr) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析字符串并转换为 LocalDateTime (默认时间为00:00:00)
        return LocalDateTime.parse(dateStr, formatter);
    }

    @Data
    public static class PageReq {
        private Integer pageNo;
        private Integer pageSize;
        private QueryBody queryBody;
    }

    @Data
    public static class QueryBody {

        @NotBlank(message = "目录id不得为空")
        private String id;
        private String name;
        private List<String> createTime;
        private List<String> updateTime;
    }

}