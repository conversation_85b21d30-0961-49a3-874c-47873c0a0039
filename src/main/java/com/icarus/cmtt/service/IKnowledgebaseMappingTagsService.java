package com.icarus.cmtt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.cmtt.dto.KnowledgebaseMappingDTO;
import com.icarus.cmtt.entity.KnowledgebaseMapping;
import com.icarus.cmtt.entity.KnowledgebaseMappingTags;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface IKnowledgebaseMappingTagsService extends IService<KnowledgebaseMappingTags> {


    // 添加
    Integer addKnowledgebaseMappingTags(KnowledgebaseMappingTags knowledgebaseMappingTags);

    // 删除
    Integer deleteByBaseId(Integer id);

    // 查询
    List<KnowledgebaseMappingTags> findByBaseId(Integer id);

    // 分页查询
    IPage<KnowledgebaseMappingTags> queryKnowledgebaseMappingTags(KnowledgebaseMappingDTO queryDTO);

}
