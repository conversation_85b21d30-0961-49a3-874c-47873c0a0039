package com.icarus.cmtt.service;

import cn.hutool.json.JSONObject;
import com.icarus.cmtt.entity.CorpusCollectionConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ICorpusCollectionConfigService extends IService<CorpusCollectionConfig> {
    List<String> selectIdsByFrequency(String frequency, String startTime, String endTime);
}
