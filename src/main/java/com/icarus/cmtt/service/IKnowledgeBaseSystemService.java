package com.icarus.cmtt.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.cmtt.dto.DifyFilesDTO;
import com.icarus.cmtt.dto.KnowledgeBaseSystemDTO;
import com.icarus.cmtt.dto.KnowledgebaseDTO;
import com.icarus.cmtt.entity.CorpusScopeRule;
import com.icarus.cmtt.entity.KnowledgeBaseSystem;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface IKnowledgeBaseSystemService extends IService<KnowledgeBaseSystem> {

    Integer addKnowledgeBaseSystem(KnowledgeBaseSystemDTO dto);

    Integer updateKnowledgeBaseSystem(KnowledgebaseDTO dto);

    Integer deleteKnowledgeBaseSystem(String id);

    KnowledgeBaseSystemDTO getKnowledgeBaseSystem(String id);

    String getKnowledgeBaseSystemTree();

    Integer renameKnowledgeBaseSystem(KnowledgeBaseSystemDTO dto);

    List<String> getNodes(String id);

    Long getCorpusCountByTags(List<CorpusScopeRule> condition);

    Object saveFiles(DifyFilesDTO difyFilesDTO);

    String getFullPath(String id);

    Object data(JSONObject jsonObject);

}
