package com.icarus.cmtt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icarus.cmtt.dto.KnowledgebaseMappingDTO;
import com.icarus.cmtt.entity.KnowledgebaseMapping;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.cmtt.vo.KnowledgebaseTagsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface IKnowledgebaseMappingService extends IService<KnowledgebaseMapping> {


    Integer addMappingTags(KnowledgebaseMappingDTO dto);

    // 添加
    Integer addKnowledgebaseMapping(KnowledgebaseMapping knowledgebaseMapping);

    // 删除
    Integer deleteKnowledgebaseMapping(Integer id);

    // 修改
    Integer updateKnowledgebaseMapping(KnowledgebaseMapping knowledgebaseMapping);

    // 查询
    KnowledgebaseMapping findKnowledgebaseMappingById(Integer id);

    // 查询所有
    List<KnowledgebaseMapping> findAllKnowledgebaseMappings();

    // 分页查询
    IPage<KnowledgebaseTagsVo> queryKnowledgebaseMappings(KnowledgebaseMappingDTO queryDTO);

    Integer updateMappingTags(KnowledgebaseMappingDTO dtoUpdate);

    KnowledgebaseTagsVo findByBaseId(Integer id);
}
