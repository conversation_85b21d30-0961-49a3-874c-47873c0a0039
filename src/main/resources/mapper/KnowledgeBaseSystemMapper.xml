<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.KnowledgeBaseSystemMapper">

    <select id="getNodes" resultType="java.lang.String">
        WITH RECURSIVE sub_tree AS (
            -- 初始节点
            SELECT id,property
            FROM knowledge_base_system
            WHERE id = #{id}

            UNION ALL

            -- 递归查找子节点
            SELECT t.id, t.property
            FROM knowledge_base_system t
                     INNER JOIN sub_tree st ON t.parent_id = st.id
        )
-- 筛选叶子节点
        SELECT id
        FROM sub_tree
        WHERE property = 'knowledgebase'
    </select>
    <select id="getFullPath" resultType="java.lang.String">
        WITH RECURSIVE ancestor_hierarchy AS (
            -- 基础查询：获取起始节点
            SELECT id, parent_id, name, 1 AS level
            FROM knowledge_base_system
            WHERE id = #{id}

            UNION ALL

            -- 递归查询：向上查找父节点
            SELECT parent.id, parent.parent_id, parent.name, child.level + 1
            FROM knowledge_base_system   parent
                     JOIN ancestor_hierarchy child ON parent.id = child.parent_id
        )
        SELECT name
        FROM ancestor_hierarchy
        ORDER BY level DESC;
    </select>

</mapper>
