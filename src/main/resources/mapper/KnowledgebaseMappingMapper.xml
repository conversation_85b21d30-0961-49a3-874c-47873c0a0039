<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.KnowledgebaseMappingMapper">
    <select id="selectKnowledgebaseTagsPage" resultType="com.icarus.cmtt.vo.KnowledgebaseTagsVo">
        SELECT
        kb.id,
        kb.name,
        kb.create_time,
        kb.update_time,
        kb.created_by,
        kb.modified_by,
        (
        SELECT STRING_AGG(tag_id::TEXT, ',')
        FROM knowledge_base_mapping_tags
        WHERE knowledgebase_mapping_id = kb.id
        ) AS tags,
        (
        SELECT COUNT(DISTINCT rct.corpus_id)
        FROM knowledge_base_mapping_tags kbt
        JOIN related_corpus_tags rct ON kbt.tag_id = rct.tag_id
        WHERE kbt.knowledgebase_mapping_id = kb.id
        ) AS corpusCount
        FROM knowledge_base_mapping kb
        <where>
            <!-- 名称模糊查询 -->
            <if test="dto.name != null and dto.name != ''">
                AND kb.name LIKE CONCAT('%', #{dto.name}, '%')
            </if>

            <!-- 标签多选条件 -->
            <if test="dto.relatedTags != null and dto.relatedTags.size() > 0">
                AND EXISTS (
                SELECT 1 FROM knowledge_base_mapping_tags
                WHERE knowledgebase_mapping_id = kb.id
                AND tag_id IN
                <foreach item="tagId" collection="dto.relatedTags" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                )
            </if>

            <!-- 创建时间范围 -->
            <if test="dto.createTimeRange != null and dto.createTimeRange.size() == 2">
                AND kb.create_time BETWEEN #{dto.createTimeRange[0]} AND #{dto.createTimeRange[1]}
            </if>

            <!-- 更新时间范围 -->
            <if test="dto.updateTimeRange != null and dto.updateTimeRange.size() == 2">
                AND kb.update_time BETWEEN #{dto.updateTimeRange[0]} AND #{dto.updateTimeRange[1]}
            </if>

            <!-- 创建人 -->
            <if test="dto.created_by != null and dto.created_by != ''">
                AND kb.created_by = #{dto.created_by}
            </if>

            <!-- 修改人 -->
            <if test="dto.modified_by != null and dto.modified_by != ''">
                AND kb.modified_by = #{dto.modified_by}
            </if>
        </where>
        <!-- 动态排序 -->
        <choose>
            <when test="dto.sort != null and dto.sort != ''">
                ORDER BY
                <choose>
                    <when test="dto.sort == 'create_time'">kb.create_time</when>
                    <when test="dto.sort == 'update_time'">kb.update_time</when>
                    <otherwise>kb.id</otherwise>
                </choose>
                <choose>
                    <when test="dto.order == 'desc'">DESC</when>
                    <otherwise>ASC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY kb.id
            </otherwise>
        </choose>
    </select>


</mapper>
