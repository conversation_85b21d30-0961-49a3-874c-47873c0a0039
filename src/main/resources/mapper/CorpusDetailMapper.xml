<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusDetailMapper">

    <!-- 根据爬虫内容ID查询语料详情 -->
    <select id="findByCrawlContentId" resultType="com.icarus.cmtt.entity.CorpusDetail">
        SELECT * FROM corpus_detail WHERE crawl_content_id = #{crawlContentId}
    </select>

    <!-- 根据ID更新收集状态 -->
    <update id="updateCollectionStatusById">
        UPDATE corpus_detail 
        SET collection_status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
