<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusFileMapper">

    <!-- 根据语料ID删除语料文件 -->
    <delete id="deleteByCorpusId">
        DELETE FROM corpus_file WHERE corpus_id = #{corpusId}
    </delete>

    <!-- 根据语料ID查询语料文件列表 -->
    <select id="findByCorpusId" resultType="com.icarus.cmtt.entity.CorpusFile">
        SELECT * FROM corpus_file WHERE corpus_id = #{corpusId}
    </select>


</mapper>
