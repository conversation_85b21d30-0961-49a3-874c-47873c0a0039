<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusSliceDocumentMapper">


    <select id="getFileIdsBySystemId" resultType="java.lang.String">
        WITH target_ids AS (
            SELECT DISTINCT ct.id
            FROM corpus_scope_rule csr
                     CROSS JOIN unnest(string_to_array(csr.value, ',')) AS split_tag_id
                     LEFT JOIN corpus_tags ct ON ct.id = split_tag_id::bigint
        WHERE csr.system_id = #{id}
            )
        SELECT DISTINCT d.id AS document_id
        FROM corpus_slice_document d
        WHERE EXISTS (
            SELECT 1
            FROM target_ids t
            WHERE t.id::text = ANY(string_to_array(trim(both '{}' from d.tags::text), ','))
        )
    </select>
</mapper>
