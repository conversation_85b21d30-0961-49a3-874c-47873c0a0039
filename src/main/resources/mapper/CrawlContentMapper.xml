<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CrawlContentMapper">

    <!-- 根据爬虫设置ID和批次ID查询爬虫内容 -->
    <select id="findByCrawlSettingIdAndCrawlBatchId" resultType="com.icarus.cmtt.entity.CrawlContent">
        SELECT * FROM crawl_content 
        WHERE crawl_setting_id = #{crawlSettingId} 
        AND crawl_batch_id = #{batchId}
    </select>

</mapper> 