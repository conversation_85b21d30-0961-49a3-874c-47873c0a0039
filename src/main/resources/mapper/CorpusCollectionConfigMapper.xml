<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusCollectionConfigMapper">

    <select id="selectIdsByFrequency" resultType="java.lang.String">
        SELECT id
        FROM corpus_collection_config
        WHERE EXISTS (
            SELECT 1
            FROM json_array_elements(frequency::json) AS elem
            WHERE (
                #{frequency}::text IS NULL
                OR (elem->>'type' IS NOT NULL AND elem->>'type' = #{frequency}::text)
                )
              AND (
                (#{startTime}::text IS NULL AND #{endTime}::text IS NULL)
                    OR (
                    elem->>'time' IS NOT NULL
                        AND (
                        (#{startTime}::text IS NOT NULL AND #{endTime}::text IS NOT NULL
                        AND (elem->>'time')::TIME BETWEEN #{startTime}::TIME AND #{endTime}::TIME)
                        OR
                        (#{startTime}::text IS NOT NULL AND #{endTime}::text IS NULL
                        AND (elem->>'time')::TIME >= #{startTime}::TIME)
                        OR
                        (#{startTime}::text IS NULL AND #{endTime}::text IS NOT NULL
                        AND #{endTime}::TIME >= (elem->>'time')::TIME)
                        )
                    )
                )
        )
    </select>
        <!-- 根据收集类型查询语料收集配置 -->
        <select id="findByCollectionChannel" resultType="com.icarus.cmtt.entity.CorpusCollectionConfig">
            SELECT * FROM corpus_collection_config WHERE collection_channel = #{collectionType}  and status=0
        </select>

</mapper>