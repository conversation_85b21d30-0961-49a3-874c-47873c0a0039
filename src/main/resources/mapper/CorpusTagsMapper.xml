<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusTagsMapper">


    <select id="getFullPathName" resultType="java.lang.String">
        WITH RECURSIVE tag_hierarchy AS (
            -- 锚点查询：选择传入的ID对应的记录
            SELECT
                id,
                name,
                parent_id,
                name::TEXT AS full_name -- 初始化full_name为当前名称
            FROM
                corpus_tags
            WHERE
                id = #{id} -- :input_id 是传入的参数

            UNION ALL
            -- 递归查询：连接到父级记录
            SELECT
                ct.id,
                ct.name,
                ct.parent_id,
                (ct.name || '-' || th.full_name)::TEXT AS full_name -- 将父级名称拼接到前面
            FROM
                corpus_tags ct
                    INNER JOIN
                tag_hierarchy th
                ON ct.id = th.parent_id -- 连接条件：当前记录的id等于上一层的parent_id
        )
-- 选择最终结果，返回从根节点到指定id节点的完整路径
        SELECT
            full_name
        FROM
            tag_hierarchy
        ORDER BY
            full_name DESC
            LIMIT 1;
    </select>
</mapper>
