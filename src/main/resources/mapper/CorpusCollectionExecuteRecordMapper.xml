<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusCollectionExecuteRecordMapper">

    <!-- 根据批次ID查询执行记录 -->
    <select id="findByBatchId" resultType="com.icarus.cmtt.entity.CorpusCollectionExecuteRecord">
        SELECT * FROM corpus_collection_execute_record 
        WHERE batch_id = #{batchId}
    </select>

    <!-- 根据语料收集配置ID和批次ID更新状态 -->
    <update id="updateByCorpusCollectionConfigIdAndBatchId">
        UPDATE corpus_collection_execute_record 
        SET execute_status = 3, update_time = NOW()
        WHERE corpus_collection_config_id = #{corpusCollectionConfigId} 
        AND batch_id = #{batchId}
    </update>

</mapper>
