<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.cmtt.mapper.CorpusSliceSegmentMapper">

    <select id="getSumLengthByTags" resultType="java.lang.Integer">
        SELECT sum(length)
        FROM corpus_slice_segment
        WHERE EXISTS (
        SELECT 1
        FROM unnest(tags) AS tag
        WHERE tag::text IN
        <foreach item="tag" collection="list" open="(" separator="," close=")">
            #{tag}
        </foreach>
        )
    </select>
    <select id="getCountByTags" resultType="java.lang.Integer">
        SELECT count(1)
        FROM corpus_slice_segment
        WHERE EXISTS (
        SELECT 1
        FROM unnest(tags) AS tag
        WHERE tag::text IN
        <foreach item="tag" collection="list" open="(" separator="," close=")">
            #{tag}
        </foreach>
        )


    </select>
    <select id="getIdsByTags" resultType="java.lang.String">
        SELECT id
        FROM corpus_slice_segment
        WHERE EXISTS (
        SELECT 1
        FROM unnest(tags) AS tag
        WHERE tag::text IN
        <foreach item="tag" collection="list" open="(" separator="," close=")">
            #{tag}
        </foreach>
        )
    </select>
</mapper>
